export * from './generators/template';
export * from './languageModule';
export * from './parsers/scriptSetupRanges';
export * from './plugins';
export * from './virtualFile/vueFile';
export * from './types';
export * from './utils/ts';
export * from './utils/parseSfc';
export * as scriptRanges from './parsers/scriptRanges';
export * as sharedTypes from './utils/globalTypes';
export * from './utils/shared';
export { tsCodegen } from './plugins/vue-tsx';
export * from '@volar/language-core';
export * from '@volar/source-map';
export type * as CompilerDOM from '@vue/compiler-dom';
//# sourceMappingURL=index.d.ts.map