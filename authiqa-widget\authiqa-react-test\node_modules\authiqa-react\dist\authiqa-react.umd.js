(function(R,l){typeof exports=="object"&&typeof module<"u"?l(exports,require("react")):typeof define=="function"&&define.amd?define(["exports","react"],l):(R=typeof globalThis<"u"?globalThis:R||self,l(<PERSON><PERSON>={},<PERSON><PERSON>))})(this,function(R,l){"use strict";var F={exports:{}},g={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $;function K(){if($)return g;$=1;var i=Symbol.for("react.transitional.element"),b=Symbol.for("react.fragment");function f(d,c,n){var u=null;if(n!==void 0&&(u=""+n),c.key!==void 0&&(u=""+c.key),"key"in c){n={};for(var m in c)m!=="key"&&(n[m]=c[m])}else n=c;return c=n.ref,{$$typeof:i,type:d,key:u,ref:c!==void 0?c:null,props:n}}return g.Fragment=b,g.jsx=f,g.jsxs=f,g}var O={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var z;function ee(){return z||(z=1,process.env.NODE_ENV!=="production"&&function(){function i(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===L?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _:return"Fragment";case N:return"Profiler";case P:return"StrictMode";case D:return"Suspense";case p:return"SuspenseList";case W:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case x:return"Portal";case Y:return(e.displayName||"Context")+".Provider";case C:return(e._context.displayName||"Context")+".Consumer";case M:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case q:return r=e.displayName||null,r!==null?r:i(e.type)||"Memo";case I:r=e._payload,e=e._init;try{return i(e(r))}catch{}}return null}function b(e){return""+e}function f(e){try{b(e);var r=!1}catch{r=!0}if(r){r=console;var t=r.error,o=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return t.call(r,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",o),b(e)}}function d(e){if(e===_)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===I)return"<...>";try{var r=i(e);return r?"<"+r+">":"<...>"}catch{return"<...>"}}function c(){var e=T.A;return e===null?null:e.getOwner()}function n(){return Error("react-stack-top-frame")}function u(e){if(k.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function m(e,r){function t(){X||(X=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",r))}t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}function S(){var e=i(this.type);return B[e]||(B[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function y(e,r,t,o,E,s,V,G){return t=s.ref,e={$$typeof:j,type:e,key:r,props:s,_owner:E},(t!==void 0?t:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:S}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:V}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:G}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function h(e,r,t,o,E,s,V,G){var a=r.children;if(a!==void 0)if(o)if(ne(a)){for(o=0;o<a.length;o++)A(a[o]);Object.freeze&&Object.freeze(a)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else A(a);if(k.call(r,"key")){a=i(e);var w=Object.keys(r).filter(function(oe){return oe!=="key"});o=0<w.length?"{key: someKey, "+w.join(": ..., ")+": ...}":"{key: someKey}",Q[a+o]||(w=0<w.length?"{"+w.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,o,a,w,a),Q[a+o]=!0)}if(a=null,t!==void 0&&(f(t),a=""+t),u(r)&&(f(r.key),a=""+r.key),"key"in r){t={};for(var J in r)J!=="key"&&(t[J]=r[J])}else t=r;return a&&m(t,typeof e=="function"?e.displayName||e.name||"Unknown":e),y(e,a,s,E,c(),t,V,G)}function A(e){typeof e=="object"&&e!==null&&e.$$typeof===j&&e._store&&(e._store.validated=1)}var v=l,j=Symbol.for("react.transitional.element"),x=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),P=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),C=Symbol.for("react.consumer"),Y=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),D=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),q=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),L=Symbol.for("react.client.reference"),T=v.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,ne=Array.isArray,U=console.createTask?console.createTask:function(){return null};v={"react-stack-bottom-frame":function(e){return e()}};var X,B={},H=v["react-stack-bottom-frame"].bind(v,n)(),Z=U(d(n)),Q={};O.Fragment=_,O.jsx=function(e,r,t,o,E){var s=1e4>T.recentlyCreatedOwnerStacks++;return h(e,r,t,!1,o,E,s?Error("react-stack-top-frame"):H,s?U(d(e)):Z)},O.jsxs=function(e,r,t,o,E){var s=1e4>T.recentlyCreatedOwnerStacks++;return h(e,r,t,!0,o,E,s?Error("react-stack-top-frame"):H,s?U(d(e)):Z)}}()),O}process.env.NODE_ENV==="production"?F.exports=K():F.exports=ee();var re=F.exports;const te=({publicKey:i,action:b="signin",theme:f="light",mode:d="popup",container:c="authiqa-react-container",onSuccess:n,onError:u,customization:m,organizationDomain:S="authiqa.com",termsAndConditions:y,privacy:h,notificationSettings:A,verifyAuthPath:v,updatePasswordPath:j,resendAuthPath:x,successAuthPath:_,signinAuthPath:P,resetAuthPath:N,disableStyles:C,messages:Y,...M})=>{const D=l.useRef(null),p=l.useRef(null),q=l.useRef(`authiqa-react-${Math.random().toString(36).substring(2,11)}`);return l.useEffect(()=>{if(typeof window.AuthiqaWidget!="function"){console.error("AuthiqaWidget not found. Make sure @authiqa/widget is properly loaded.");return}const I={publicKey:i,container:q.current,mode:d,theme:f,organizationDomain:S,termsAndConditions:y,privacy:h,notificationSettings:A,customization:m,verifyAuthPath:v,updatePasswordPath:j,resendAuthPath:x,successAuthPath:_,signinAuthPath:P,resetAuthPath:N,disableStyles:C,messages:Y,...M},W=new window.AuthiqaWidget(I);p.current=W,W.show(b);const L=k=>{n&&n(k)},T=k=>{u&&u(k)};return n&&document.addEventListener("authiqa:success",L),u&&document.addEventListener("authiqa:error",T),()=>{n&&document.removeEventListener("authiqa:success",L),u&&document.removeEventListener("authiqa:error",T),p.current&&typeof p.current.destroy=="function"&&p.current.destroy()}},[i,b,f,d,n,u,m,S,y,h,A,v,j,x,_,P,N,C,Y]),re.jsx("div",{id:q.current,ref:D})};R.AuthiqaWidget=te,Object.defineProperty(R,Symbol.toStringTag,{value:"Module"})});
