#!/usr/bin/env node

/**
 * Package Verification Script
 * Verifies that both packages are properly built and ready for deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Authiqa Packages...\n');

// Check main widget package
console.log('📦 Main Widget Package (@authiqa/widget)');
const mainPackageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
console.log(`   Version: ${mainPackageJson.version}`);

const mainDistFiles = [
    './dist/index.js',
    './dist/index.d.ts',
    './dist/hash-navigation.js'
];

let mainPackageValid = true;
mainDistFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        console.log(`   ✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
    } else {
        console.log(`   ❌ ${file} - Missing!`);
        mainPackageValid = false;
    }
});

// Check React package
console.log('\n📦 React Package (authiqa-react)');
const reactPackageJson = JSON.parse(fs.readFileSync('./authiqa-react/package.json', 'utf8'));
console.log(`   Version: ${reactPackageJson.version}`);

const reactDistFiles = [
    './authiqa-react/dist/authiqa-react.mjs',
    './authiqa-react/dist/authiqa-react.umd.js',
    './authiqa-react/dist/index.d.ts',
    './authiqa-react/dist/types.d.ts'
];

let reactPackageValid = true;
reactDistFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        console.log(`   ✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
    } else {
        console.log(`   ❌ ${file} - Missing!`);
        reactPackageValid = false;
    }
});

// Check for package tarball
const reactTarball = './authiqa-react/authiqa-react-1.1.0.tgz';
if (fs.existsSync(reactTarball)) {
    const stats = fs.statSync(reactTarball);
    console.log(`   ✅ ${reactTarball} (${(stats.size / 1024).toFixed(1)}KB)`);
} else {
    console.log(`   ⚠️  ${reactTarball} - Not found (run 'npm pack' in authiqa-react folder)`);
}

// Summary
console.log('\n📋 Summary:');
if (mainPackageValid && reactPackageValid) {
    console.log('✅ All packages are ready for deployment!');
    console.log('\nNext steps:');
    console.log('1. Review DEPLOYMENT_INSTRUCTIONS.md');
    console.log('2. Run npm publish in each package directory');
    console.log('3. Update CDN files if applicable');
} else {
    console.log('❌ Some packages have issues. Please rebuild before deploying.');
}

console.log('\n🔗 Package Versions:');
console.log(`   Main Widget: ${mainPackageJson.version}`);
console.log(`   React Package: ${reactPackageJson.version}`);
