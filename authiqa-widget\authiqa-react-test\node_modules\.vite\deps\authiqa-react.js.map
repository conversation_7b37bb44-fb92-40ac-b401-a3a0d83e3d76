{"version": 3, "sources": ["../../authiqa-react/dist/authiqa-react.mjs"], "sourcesContent": ["import ee, { useRef as U, useEffect as re } from \"react\";\nvar V = { exports: {} }, O = {};\n/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nvar H;\nfunction te() {\n  if (H)\n    return O;\n  H = 1;\n  var i = Symbol.for(\"react.transitional.element\"), E = Symbol.for(\"react.fragment\");\n  function l(f, c, n) {\n    var u = null;\n    if (n !== void 0 && (u = \"\" + n), c.key !== void 0 && (u = \"\" + c.key), \"key\" in c) {\n      n = {};\n      for (var d in c)\n        d !== \"key\" && (n[d] = c[d]);\n    } else\n      n = c;\n    return c = n.ref, {\n      $$typeof: i,\n      type: f,\n      key: u,\n      ref: c !== void 0 ? c : null,\n      props: n\n    };\n  }\n  return O.Fragment = E, O.jsx = l, O.jsxs = l, O;\n}\nvar A = {};\n/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nvar Z;\nfunction ne() {\n  return Z || (Z = 1, process.env.NODE_ENV !== \"production\" && function() {\n    function i(e) {\n      if (e == null)\n        return null;\n      if (typeof e == \"function\")\n        return e.$$typeof === I ? null : e.displayName || e.name || null;\n      if (typeof e == \"string\")\n        return e;\n      switch (e) {\n        case b:\n          return \"Fragment\";\n        case y:\n          return \"Profiler\";\n        case x:\n          return \"StrictMode\";\n        case L:\n          return \"Suspense\";\n        case v:\n          return \"SuspenseList\";\n        case q:\n          return \"Activity\";\n      }\n      if (typeof e == \"object\")\n        switch (typeof e.tag == \"number\" && console.error(\n          \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n        ), e.$$typeof) {\n          case S:\n            return \"Portal\";\n          case N:\n            return (e.displayName || \"Context\") + \".Provider\";\n          case P:\n            return (e._context.displayName || \"Context\") + \".Consumer\";\n          case $:\n            var r = e.render;\n            return e = e.displayName, e || (e = r.displayName || r.name || \"\", e = e !== \"\" ? \"ForwardRef(\" + e + \")\" : \"ForwardRef\"), e;\n          case C:\n            return r = e.displayName || null, r !== null ? r : i(e.type) || \"Memo\";\n          case Y:\n            r = e._payload, e = e._init;\n            try {\n              return i(e(r));\n            } catch {\n            }\n        }\n      return null;\n    }\n    function E(e) {\n      return \"\" + e;\n    }\n    function l(e) {\n      try {\n        E(e);\n        var r = !1;\n      } catch {\n        r = !0;\n      }\n      if (r) {\n        r = console;\n        var t = r.error, o = typeof Symbol == \"function\" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || \"Object\";\n        return t.call(\n          r,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          o\n        ), E(e);\n      }\n    }\n    function f(e) {\n      if (e === b)\n        return \"<>\";\n      if (typeof e == \"object\" && e !== null && e.$$typeof === Y)\n        return \"<...>\";\n      try {\n        var r = i(e);\n        return r ? \"<\" + r + \">\" : \"<...>\";\n      } catch {\n        return \"<...>\";\n      }\n    }\n    function c() {\n      var e = _.A;\n      return e === null ? null : e.getOwner();\n    }\n    function n() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function u(e) {\n      if (T.call(e, \"key\")) {\n        var r = Object.getOwnPropertyDescriptor(e, \"key\").get;\n        if (r && r.isReactWarning)\n          return !1;\n      }\n      return e.key !== void 0;\n    }\n    function d(e, r) {\n      function t() {\n        G || (G = !0, console.error(\n          \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n          r\n        ));\n      }\n      t.isReactWarning = !0, Object.defineProperty(e, \"key\", {\n        get: t,\n        configurable: !0\n      });\n    }\n    function h() {\n      var e = i(this.type);\n      return J[e] || (J[e] = !0, console.error(\n        \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n      )), e = this.props.ref, e !== void 0 ? e : null;\n    }\n    function j(e, r, t, o, m, s, F, M) {\n      return t = s.ref, e = {\n        $$typeof: g,\n        type: e,\n        key: r,\n        props: s,\n        _owner: m\n      }, (t !== void 0 ? t : null) !== null ? Object.defineProperty(e, \"ref\", {\n        enumerable: !1,\n        get: h\n      }) : Object.defineProperty(e, \"ref\", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      }), Object.defineProperty(e, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      }), Object.defineProperty(e, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: F\n      }), Object.defineProperty(e, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: M\n      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;\n    }\n    function k(e, r, t, o, m, s, F, M) {\n      var a = r.children;\n      if (a !== void 0)\n        if (o)\n          if (Q(a)) {\n            for (o = 0; o < a.length; o++)\n              w(a[o]);\n            Object.freeze && Object.freeze(a);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else\n          w(a);\n      if (T.call(r, \"key\")) {\n        a = i(e);\n        var p = Object.keys(r).filter(function(K) {\n          return K !== \"key\";\n        });\n        o = 0 < p.length ? \"{key: someKey, \" + p.join(\": ..., \") + \": ...}\" : \"{key: someKey}\", B[a + o] || (p = 0 < p.length ? \"{\" + p.join(\": ..., \") + \": ...}\" : \"{}\", console.error(\n          `A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,\n          o,\n          a,\n          p,\n          a\n        ), B[a + o] = !0);\n      }\n      if (a = null, t !== void 0 && (l(t), a = \"\" + t), u(r) && (l(r.key), a = \"\" + r.key), \"key\" in r) {\n        t = {};\n        for (var D in r)\n          D !== \"key\" && (t[D] = r[D]);\n      } else\n        t = r;\n      return a && d(\n        t,\n        typeof e == \"function\" ? e.displayName || e.name || \"Unknown\" : e\n      ), j(\n        e,\n        a,\n        s,\n        m,\n        c(),\n        t,\n        F,\n        M\n      );\n    }\n    function w(e) {\n      typeof e == \"object\" && e !== null && e.$$typeof === g && e._store && (e._store.validated = 1);\n    }\n    var R = ee, g = Symbol.for(\"react.transitional.element\"), S = Symbol.for(\"react.portal\"), b = Symbol.for(\"react.fragment\"), x = Symbol.for(\"react.strict_mode\"), y = Symbol.for(\"react.profiler\"), P = Symbol.for(\"react.consumer\"), N = Symbol.for(\"react.context\"), $ = Symbol.for(\"react.forward_ref\"), L = Symbol.for(\"react.suspense\"), v = Symbol.for(\"react.suspense_list\"), C = Symbol.for(\"react.memo\"), Y = Symbol.for(\"react.lazy\"), q = Symbol.for(\"react.activity\"), I = Symbol.for(\"react.client.reference\"), _ = R.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, T = Object.prototype.hasOwnProperty, Q = Array.isArray, W = console.createTask ? console.createTask : function() {\n      return null;\n    };\n    R = {\n      \"react-stack-bottom-frame\": function(e) {\n        return e();\n      }\n    };\n    var G, J = {}, z = R[\"react-stack-bottom-frame\"].bind(\n      R,\n      n\n    )(), X = W(f(n)), B = {};\n    A.Fragment = b, A.jsx = function(e, r, t, o, m) {\n      var s = 1e4 > _.recentlyCreatedOwnerStacks++;\n      return k(\n        e,\n        r,\n        t,\n        !1,\n        o,\n        m,\n        s ? Error(\"react-stack-top-frame\") : z,\n        s ? W(f(e)) : X\n      );\n    }, A.jsxs = function(e, r, t, o, m) {\n      var s = 1e4 > _.recentlyCreatedOwnerStacks++;\n      return k(\n        e,\n        r,\n        t,\n        !0,\n        o,\n        m,\n        s ? Error(\"react-stack-top-frame\") : z,\n        s ? W(f(e)) : X\n      );\n    };\n  }()), A;\n}\nprocess.env.NODE_ENV === \"production\" ? V.exports = te() : V.exports = ne();\nvar oe = V.exports;\nconst ce = ({\n  publicKey: i,\n  action: E = \"signin\",\n  theme: l = \"light\",\n  mode: f = \"popup\",\n  container: c = \"authiqa-react-container\",\n  onSuccess: n,\n  onError: u,\n  customization: d,\n  organizationDomain: h = \"authiqa.com\",\n  termsAndConditions: j,\n  privacy: k,\n  notificationSettings: w,\n  verifyAuthPath: R,\n  updatePasswordPath: g,\n  resendAuthPath: S,\n  successAuthPath: b,\n  signinAuthPath: x,\n  resetAuthPath: y,\n  disableStyles: P,\n  messages: N,\n  ...$\n}) => {\n  const L = U(null), v = U(null), C = U(`authiqa-react-${Math.random().toString(36).substring(2, 11)}`);\n  return re(() => {\n    if (typeof window.AuthiqaWidget != \"function\") {\n      console.error(\"AuthiqaWidget not found. Make sure @authiqa/widget is properly loaded.\");\n      return;\n    }\n    const Y = {\n      publicKey: i,\n      container: C.current,\n      mode: f,\n      theme: l,\n      organizationDomain: h,\n      termsAndConditions: j,\n      privacy: k,\n      notificationSettings: w,\n      customization: d,\n      verifyAuthPath: R,\n      updatePasswordPath: g,\n      resendAuthPath: S,\n      successAuthPath: b,\n      signinAuthPath: x,\n      resetAuthPath: y,\n      disableStyles: P,\n      messages: N,\n      ...$\n    }, q = new window.AuthiqaWidget(Y);\n    v.current = q, q.show(E);\n    const I = (T) => {\n      n && n(T);\n    }, _ = (T) => {\n      u && u(T);\n    };\n    return n && document.addEventListener(\"authiqa:success\", I), u && document.addEventListener(\"authiqa:error\", _), () => {\n      n && document.removeEventListener(\"authiqa:success\", I), u && document.removeEventListener(\"authiqa:error\", _), v.current && typeof v.current.destroy == \"function\" && v.current.destroy();\n    };\n  }, [\n    i,\n    E,\n    l,\n    f,\n    n,\n    u,\n    d,\n    h,\n    j,\n    k,\n    w,\n    R,\n    g,\n    S,\n    b,\n    x,\n    y,\n    P,\n    N\n  ]), /* @__PURE__ */ oe.jsx(\"div\", { id: C.current, ref: L });\n};\nexport {\n  ce as AuthiqaWidget\n};\n"], "mappings": ";;;;;;AAAA,mBAAiD;AACjD,IAAI,IAAI,EAAE,SAAS,CAAC,EAAE;AAkCtB,IAAI,IAAI,CAAC;AAUT,IAAI;AACJ,SAAS,KAAK;AACZ,SAAO,MAAM,IAAI,GAA4C,WAAW;AACtE,aAAS,EAAE,GAAG;AACZ,UAAI,KAAK;AACP,eAAO;AACT,UAAI,OAAO,KAAK;AACd,eAAO,EAAE,aAAa,IAAI,OAAO,EAAE,eAAe,EAAE,QAAQ;AAC9D,UAAI,OAAO,KAAK;AACd,eAAO;AACT,cAAQ,GAAG;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,MACX;AACA,UAAI,OAAO,KAAK;AACd,gBAAQ,OAAO,EAAE,OAAO,YAAY,QAAQ;AAAA,UAC1C;AAAA,QACF,GAAG,EAAE,UAAU;AAAA,UACb,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,oBAAQ,EAAE,eAAe,aAAa;AAAA,UACxC,KAAK;AACH,oBAAQ,EAAE,SAAS,eAAe,aAAa;AAAA,UACjD,KAAK;AACH,gBAAI,IAAI,EAAE;AACV,mBAAO,IAAI,EAAE,aAAa,MAAM,IAAI,EAAE,eAAe,EAAE,QAAQ,IAAI,IAAI,MAAM,KAAK,gBAAgB,IAAI,MAAM,eAAe;AAAA,UAC7H,KAAK;AACH,mBAAO,IAAI,EAAE,eAAe,MAAM,MAAM,OAAO,IAAI,EAAE,EAAE,IAAI,KAAK;AAAA,UAClE,KAAK;AACH,gBAAI,EAAE,UAAU,IAAI,EAAE;AACtB,gBAAI;AACF,qBAAO,EAAE,EAAE,CAAC,CAAC;AAAA,YACf,QAAQ;AAAA,YACR;AAAA,QACJ;AACF,aAAO;AAAA,IACT;AACA,aAAS,EAAE,GAAG;AACZ,aAAO,KAAK;AAAA,IACd;AACA,aAAS,EAAE,GAAG;AACZ,UAAI;AACF,UAAE,CAAC;AACH,YAAI,IAAI;AAAA,MACV,QAAQ;AACN,YAAI;AAAA,MACN;AACA,UAAI,GAAG;AACL,YAAI;AACJ,YAAI,IAAI,EAAE,OAAO,IAAI,OAAO,UAAU,cAAc,OAAO,eAAe,EAAE,OAAO,WAAW,KAAK,EAAE,YAAY,QAAQ;AACzH,eAAO,EAAE;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AACA,aAAS,EAAE,GAAG;AACZ,UAAI,MAAM;AACR,eAAO;AACT,UAAI,OAAO,KAAK,YAAY,MAAM,QAAQ,EAAE,aAAa;AACvD,eAAO;AACT,UAAI;AACF,YAAI,IAAI,EAAE,CAAC;AACX,eAAO,IAAI,MAAM,IAAI,MAAM;AAAA,MAC7B,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI;AACX,UAAI,IAAI,EAAE;AACV,aAAO,MAAM,OAAO,OAAO,EAAE,SAAS;AAAA,IACxC;AACA,aAAS,IAAI;AACX,aAAO,MAAM,uBAAuB;AAAA,IACtC;AACA,aAAS,EAAE,GAAG;AACZ,UAAI,EAAE,KAAK,GAAG,KAAK,GAAG;AACpB,YAAI,IAAI,OAAO,yBAAyB,GAAG,KAAK,EAAE;AAClD,YAAI,KAAK,EAAE;AACT,iBAAO;AAAA,MACX;AACA,aAAO,EAAE,QAAQ;AAAA,IACnB;AACA,aAAS,EAAE,GAAG,GAAG;AACf,eAAS,IAAI;AACX,cAAM,IAAI,MAAI,QAAQ;AAAA,UACpB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,QAAE,iBAAiB,MAAI,OAAO,eAAe,GAAG,OAAO;AAAA,QACrD,KAAK;AAAA,QACL,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,aAAS,IAAI;AACX,UAAI,IAAI,EAAE,KAAK,IAAI;AACnB,aAAO,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,MAAI,QAAQ;AAAA,QACjC;AAAA,MACF,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,SAAS,IAAI;AAAA,IAC7C;AACA,aAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC,aAAO,IAAI,EAAE,KAAK,IAAI;AAAA,QACpB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,IAAI,MAAM,SAAS,IAAI,UAAU,OAAO,OAAO,eAAe,GAAG,OAAO;AAAA,QACtE,YAAY;AAAA,QACZ,KAAK;AAAA,MACP,CAAC,IAAI,OAAO,eAAe,GAAG,OAAO,EAAE,YAAY,OAAI,OAAO,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,OAAO,eAAe,EAAE,QAAQ,aAAa;AAAA,QACjI,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC,GAAG,OAAO,eAAe,GAAG,cAAc;AAAA,QACzC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC,GAAG,OAAO,eAAe,GAAG,eAAe;AAAA,QAC1C,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC,GAAG,OAAO,eAAe,GAAG,cAAc;AAAA,QACzC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC,GAAG,OAAO,WAAW,OAAO,OAAO,EAAE,KAAK,GAAG,OAAO,OAAO,CAAC,IAAI;AAAA,IACnE;AACA,aAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC,UAAI,IAAI,EAAE;AACV,UAAI,MAAM;AACR,YAAI;AACF,cAAI,EAAE,CAAC,GAAG;AACR,iBAAK,IAAI,GAAG,IAAI,EAAE,QAAQ;AACxB,gBAAE,EAAE,CAAC,CAAC;AACR,mBAAO,UAAU,OAAO,OAAO,CAAC;AAAA,UAClC;AACE,oBAAQ;AAAA,cACN;AAAA,YACF;AAAA;AAEF,YAAE,CAAC;AACP,UAAI,EAAE,KAAK,GAAG,KAAK,GAAG;AACpB,YAAI,EAAE,CAAC;AACP,YAAI,IAAI,OAAO,KAAK,CAAC,EAAE,OAAO,SAAS,GAAG;AACxC,iBAAO,MAAM;AAAA,QACf,CAAC;AACD,YAAI,IAAI,EAAE,SAAS,oBAAoB,EAAE,KAAK,SAAS,IAAI,WAAW,kBAAkB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,SAAS,MAAM,EAAE,KAAK,SAAS,IAAI,WAAW,MAAM,QAAQ;AAAA,UACzK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG,EAAE,IAAI,CAAC,IAAI;AAAA,MAChB;AACA,UAAI,IAAI,MAAM,MAAM,WAAW,EAAE,CAAC,GAAG,IAAI,KAAK,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,IAAI,KAAK,EAAE,MAAM,SAAS,GAAG;AAChG,YAAI,CAAC;AACL,iBAAS,KAAK;AACZ,gBAAM,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MAC9B;AACE,YAAI;AACN,aAAO,KAAK;AAAA,QACV;AAAA,QACA,OAAO,KAAK,aAAa,EAAE,eAAe,EAAE,QAAQ,YAAY;AAAA,MAClE,GAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,EAAE,GAAG;AACZ,aAAO,KAAK,YAAY,MAAM,QAAQ,EAAE,aAAa,KAAK,EAAE,WAAW,EAAE,OAAO,YAAY;AAAA,IAC9F;AACA,QAAI,IAAI,aAAAA,SAAI,IAAI,OAAO,IAAI,4BAA4B,GAAG,IAAI,OAAO,IAAI,cAAc,GAAG,IAAI,OAAO,IAAI,gBAAgB,GAAG,IAAI,OAAO,IAAI,mBAAmB,GAAG,IAAI,OAAO,IAAI,gBAAgB,GAAG,IAAI,OAAO,IAAI,gBAAgB,GAAG,IAAI,OAAO,IAAI,eAAe,GAAG,IAAI,OAAO,IAAI,mBAAmB,GAAG,IAAI,OAAO,IAAI,gBAAgB,GAAG,IAAI,OAAO,IAAI,qBAAqB,GAAG,IAAI,OAAO,IAAI,YAAY,GAAG,IAAI,OAAO,IAAI,YAAY,GAAG,IAAI,OAAO,IAAI,gBAAgB,GAAG,IAAI,OAAO,IAAI,wBAAwB,GAAG,IAAI,EAAE,iEAAiE,IAAI,OAAO,UAAU,gBAAgB,IAAI,MAAM,SAAS,IAAI,QAAQ,aAAa,QAAQ,aAAa,WAAW;AAClrB,aAAO;AAAA,IACT;AACA,QAAI;AAAA,MACF,4BAA4B,SAAS,GAAG;AACtC,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AACA,QAAI,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,0BAA0B,EAAE;AAAA,MAC/C;AAAA,MACA;AAAA,IACF,EAAE,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;AACvB,MAAE,WAAW,GAAG,EAAE,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AAC9C,UAAI,IAAI,MAAM,EAAE;AAChB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI,MAAM,uBAAuB,IAAI;AAAA,QACrC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI;AAAA,MAChB;AAAA,IACF,GAAG,EAAE,OAAO,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,UAAI,IAAI,MAAM,EAAE;AAChB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI,MAAM,uBAAuB,IAAI;AAAA,QACrC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF,EAAE,IAAI;AACR;AACA,QAAwC,EAAE,UAAU,GAAG,IAAI,EAAE,UAAU,GAAG;AAC1E,IAAI,KAAK,EAAE;AACX,IAAM,KAAK,CAAC;AAAA,EACV,WAAW;AAAA,EACX,QAAQ,IAAI;AAAA,EACZ,OAAO,IAAI;AAAA,EACX,MAAM,IAAI;AAAA,EACV,WAAW,IAAI;AAAA,EACf,WAAW;AAAA,EACX,SAAS;AAAA,EACT,eAAe;AAAA,EACf,oBAAoB,IAAI;AAAA,EACxB,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,UAAU;AAAA,EACV,GAAG;AACL,MAAM;AACJ,QAAM,QAAI,aAAAC,QAAE,IAAI,GAAG,QAAI,aAAAA,QAAE,IAAI,GAAG,QAAI,aAAAA,QAAE,iBAAiB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE;AACpG,aAAO,aAAAC,WAAG,MAAM;AACd,QAAI,OAAO,OAAO,iBAAiB,YAAY;AAC7C,cAAQ,MAAM,wEAAwE;AACtF;AAAA,IACF;AACA,UAAM,IAAI;AAAA,MACR,WAAW;AAAA,MACX,WAAW,EAAE;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,SAAS;AAAA,MACT,sBAAsB;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,UAAU;AAAA,MACV,GAAG;AAAA,IACL,GAAG,IAAI,IAAI,OAAO,cAAc,CAAC;AACjC,MAAE,UAAU,GAAG,EAAE,KAAK,CAAC;AACvB,UAAM,IAAI,CAAC,MAAM;AACf,WAAK,EAAE,CAAC;AAAA,IACV,GAAG,IAAI,CAAC,MAAM;AACZ,WAAK,EAAE,CAAC;AAAA,IACV;AACA,WAAO,KAAK,SAAS,iBAAiB,mBAAmB,CAAC,GAAG,KAAK,SAAS,iBAAiB,iBAAiB,CAAC,GAAG,MAAM;AACrH,WAAK,SAAS,oBAAoB,mBAAmB,CAAC,GAAG,KAAK,SAAS,oBAAoB,iBAAiB,CAAC,GAAG,EAAE,WAAW,OAAO,EAAE,QAAQ,WAAW,cAAc,EAAE,QAAQ,QAAQ;AAAA,IAC3L;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAmB,GAAG,IAAI,OAAO,EAAE,IAAI,EAAE,SAAS,KAAK,EAAE,CAAC;AAC7D;", "names": ["ee", "U", "re"]}