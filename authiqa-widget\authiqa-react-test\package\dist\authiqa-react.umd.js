(function(l,d){typeof exports=="object"&&typeof module<"u"?d(exports,require("react")):typeof define=="function"&&define.amd?define(["exports","react"],d):(l=typeof globalThis<"u"?globalThis:l||self,d(l.<PERSON>ct={},l.<PERSON>))})(this,function(l,d){"use strict";var T={exports:{}},m={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var x;function L(){if(x)return m;x=1;var i=Symbol.for("react.transitional.element"),p=Symbol.for("react.fragment");function R(b,a,s){var _=null;if(s!==void 0&&(_=""+s),a.key!==void 0&&(_=""+a.key),"key"in a){s={};for(var v in a)v!=="key"&&(s[v]=a[v])}else s=a;return a=s.ref,{$$typeof:i,type:b,key:_,ref:a!==void 0?a:null,props:s}}return m.Fragment=p,m.jsx=R,m.jsxs=R,m}var E={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var w;function U(){return w||(w=1,process.env.NODE_ENV!=="production"&&function(){function i(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===oe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case O:return"Fragment";case B:return"Profiler";case X:return"StrictMode";case ee:return"Suspense";case re:return"SuspenseList";case ne:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case H:return"Portal";case Q:return(e.displayName||"Context")+".Provider";case Z:return(e._context.displayName||"Context")+".Consumer";case K:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case te:return r=e.displayName||null,r!==null?r:i(e.type)||"Memo";case Y:r=e._payload,e=e._init;try{return i(e(r))}catch{}}return null}function p(e){return""+e}function R(e){try{p(e);var r=!1}catch{r=!0}if(r){r=console;var t=r.error,n=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return t.call(r,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",n),p(e)}}function b(e){if(e===O)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===Y)return"<...>";try{var r=i(e);return r?"<"+r+">":"<...>"}catch{return"<...>"}}function a(){var e=A.A;return e===null?null:e.getOwner()}function s(){return Error("react-stack-top-frame")}function _(e){if(I.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function v(e,r){function t(){F||(F=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",r))}t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}function V(){var e=i(this.type);return W[e]||(W[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function G(e,r,t,n,c,u,P,j){return t=u.ref,e={$$typeof:C,type:e,key:r,props:u,_owner:c},(t!==void 0?t:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:V}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:P}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:j}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function y(e,r,t,n,c,u,P,j){var o=r.children;if(o!==void 0)if(n)if(ae(o)){for(n=0;n<o.length;n++)N(o[n]);Object.freeze&&Object.freeze(o)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else N(o);if(I.call(r,"key")){o=i(e);var f=Object.keys(r).filter(function(se){return se!=="key"});n=0<f.length?"{key: someKey, "+f.join(": ..., ")+": ...}":"{key: someKey}",M[o+n]||(f=0<f.length?"{"+f.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,n,o,f,o),M[o+n]=!0)}if(o=null,t!==void 0&&(R(t),o=""+t),_(r)&&(R(r.key),o=""+r.key),"key"in r){t={};for(var h in r)h!=="key"&&(t[h]=r[h])}else t=r;return o&&v(t,typeof e=="function"?e.displayName||e.name||"Unknown":e),G(e,o,u,c,a(),t,P,j)}function N(e){typeof e=="object"&&e!==null&&e.$$typeof===C&&e._store&&(e._store.validated=1)}var k=d,C=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),X=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),Z=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),ee=Symbol.for("react.suspense"),re=Symbol.for("react.suspense_list"),te=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),ne=Symbol.for("react.activity"),oe=Symbol.for("react.client.reference"),A=k.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=Object.prototype.hasOwnProperty,ae=Array.isArray,S=console.createTask?console.createTask:function(){return null};k={"react-stack-bottom-frame":function(e){return e()}};var F,W={},D=k["react-stack-bottom-frame"].bind(k,s)(),$=S(b(s)),M={};E.Fragment=O,E.jsx=function(e,r,t,n,c){var u=1e4>A.recentlyCreatedOwnerStacks++;return y(e,r,t,!1,n,c,u?Error("react-stack-top-frame"):D,u?S(b(e)):$)},E.jsxs=function(e,r,t,n,c){var u=1e4>A.recentlyCreatedOwnerStacks++;return y(e,r,t,!0,n,c,u?Error("react-stack-top-frame"):D,u?S(b(e)):$)}}()),E}var g;function q(){return g||(g=1,process.env.NODE_ENV==="production"?T.exports=L():T.exports=U()),T.exports}var J=q();function z(){return J.jsx("div",{children:"Hello World"})}l.HelloWorld=z,Object.defineProperty(l,Symbol.toStringTag,{value:"Module"})});
