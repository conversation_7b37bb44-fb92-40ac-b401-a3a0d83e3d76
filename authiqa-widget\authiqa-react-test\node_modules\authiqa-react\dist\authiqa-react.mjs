import ee, { useRef as U, useEffect as re } from "react";
var V = { exports: {} }, O = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var H;
function te() {
  if (H)
    return O;
  H = 1;
  var i = Symbol.for("react.transitional.element"), E = Symbol.for("react.fragment");
  function l(f, c, n) {
    var u = null;
    if (n !== void 0 && (u = "" + n), c.key !== void 0 && (u = "" + c.key), "key" in c) {
      n = {};
      for (var d in c)
        d !== "key" && (n[d] = c[d]);
    } else
      n = c;
    return c = n.ref, {
      $$typeof: i,
      type: f,
      key: u,
      ref: c !== void 0 ? c : null,
      props: n
    };
  }
  return O.Fragment = E, O.jsx = l, O.jsxs = l, O;
}
var A = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Z;
function ne() {
  return Z || (Z = 1, process.env.NODE_ENV !== "production" && function() {
    function i(e) {
      if (e == null)
        return null;
      if (typeof e == "function")
        return e.$$typeof === I ? null : e.displayName || e.name || null;
      if (typeof e == "string")
        return e;
      switch (e) {
        case b:
          return "Fragment";
        case y:
          return "Profiler";
        case x:
          return "StrictMode";
        case L:
          return "Suspense";
        case v:
          return "SuspenseList";
        case q:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case S:
            return "Portal";
          case N:
            return (e.displayName || "Context") + ".Provider";
          case P:
            return (e._context.displayName || "Context") + ".Consumer";
          case $:
            var r = e.render;
            return e = e.displayName, e || (e = r.displayName || r.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case C:
            return r = e.displayName || null, r !== null ? r : i(e.type) || "Memo";
          case Y:
            r = e._payload, e = e._init;
            try {
              return i(e(r));
            } catch {
            }
        }
      return null;
    }
    function E(e) {
      return "" + e;
    }
    function l(e) {
      try {
        E(e);
        var r = !1;
      } catch {
        r = !0;
      }
      if (r) {
        r = console;
        var t = r.error, o = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return t.call(
          r,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          o
        ), E(e);
      }
    }
    function f(e) {
      if (e === b)
        return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === Y)
        return "<...>";
      try {
        var r = i(e);
        return r ? "<" + r + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function c() {
      var e = _.A;
      return e === null ? null : e.getOwner();
    }
    function n() {
      return Error("react-stack-top-frame");
    }
    function u(e) {
      if (T.call(e, "key")) {
        var r = Object.getOwnPropertyDescriptor(e, "key").get;
        if (r && r.isReactWarning)
          return !1;
      }
      return e.key !== void 0;
    }
    function d(e, r) {
      function t() {
        G || (G = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          r
        ));
      }
      t.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: t,
        configurable: !0
      });
    }
    function h() {
      var e = i(this.type);
      return J[e] || (J[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function j(e, r, t, o, m, s, F, M) {
      return t = s.ref, e = {
        $$typeof: g,
        type: e,
        key: r,
        props: s,
        _owner: m
      }, (t !== void 0 ? t : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: h
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: F
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: M
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function k(e, r, t, o, m, s, F, M) {
      var a = r.children;
      if (a !== void 0)
        if (o)
          if (Q(a)) {
            for (o = 0; o < a.length; o++)
              w(a[o]);
            Object.freeze && Object.freeze(a);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else
          w(a);
      if (T.call(r, "key")) {
        a = i(e);
        var p = Object.keys(r).filter(function(K) {
          return K !== "key";
        });
        o = 0 < p.length ? "{key: someKey, " + p.join(": ..., ") + ": ...}" : "{key: someKey}", B[a + o] || (p = 0 < p.length ? "{" + p.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          o,
          a,
          p,
          a
        ), B[a + o] = !0);
      }
      if (a = null, t !== void 0 && (l(t), a = "" + t), u(r) && (l(r.key), a = "" + r.key), "key" in r) {
        t = {};
        for (var D in r)
          D !== "key" && (t[D] = r[D]);
      } else
        t = r;
      return a && d(
        t,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), j(
        e,
        a,
        s,
        m,
        c(),
        t,
        F,
        M
      );
    }
    function w(e) {
      typeof e == "object" && e !== null && e.$$typeof === g && e._store && (e._store.validated = 1);
    }
    var R = ee, g = Symbol.for("react.transitional.element"), S = Symbol.for("react.portal"), b = Symbol.for("react.fragment"), x = Symbol.for("react.strict_mode"), y = Symbol.for("react.profiler"), P = Symbol.for("react.consumer"), N = Symbol.for("react.context"), $ = Symbol.for("react.forward_ref"), L = Symbol.for("react.suspense"), v = Symbol.for("react.suspense_list"), C = Symbol.for("react.memo"), Y = Symbol.for("react.lazy"), q = Symbol.for("react.activity"), I = Symbol.for("react.client.reference"), _ = R.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, T = Object.prototype.hasOwnProperty, Q = Array.isArray, W = console.createTask ? console.createTask : function() {
      return null;
    };
    R = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var G, J = {}, z = R["react-stack-bottom-frame"].bind(
      R,
      n
    )(), X = W(f(n)), B = {};
    A.Fragment = b, A.jsx = function(e, r, t, o, m) {
      var s = 1e4 > _.recentlyCreatedOwnerStacks++;
      return k(
        e,
        r,
        t,
        !1,
        o,
        m,
        s ? Error("react-stack-top-frame") : z,
        s ? W(f(e)) : X
      );
    }, A.jsxs = function(e, r, t, o, m) {
      var s = 1e4 > _.recentlyCreatedOwnerStacks++;
      return k(
        e,
        r,
        t,
        !0,
        o,
        m,
        s ? Error("react-stack-top-frame") : z,
        s ? W(f(e)) : X
      );
    };
  }()), A;
}
process.env.NODE_ENV === "production" ? V.exports = te() : V.exports = ne();
var oe = V.exports;
const ce = ({
  publicKey: i,
  action: E = "signin",
  theme: l = "light",
  mode: f = "popup",
  container: c = "authiqa-react-container",
  onSuccess: n,
  onError: u,
  customization: d,
  organizationDomain: h = "authiqa.com",
  termsAndConditions: j,
  privacy: k,
  notificationSettings: w,
  verifyAuthPath: R,
  updatePasswordPath: g,
  resendAuthPath: S,
  successAuthPath: b,
  signinAuthPath: x,
  resetAuthPath: y,
  disableStyles: P,
  messages: N,
  ...$
}) => {
  const L = U(null), v = U(null), C = U(`authiqa-react-${Math.random().toString(36).substring(2, 11)}`);
  return re(() => {
    if (typeof window.AuthiqaWidget != "function") {
      console.error("AuthiqaWidget not found. Make sure @authiqa/widget is properly loaded.");
      return;
    }
    const Y = {
      publicKey: i,
      container: C.current,
      mode: f,
      theme: l,
      organizationDomain: h,
      termsAndConditions: j,
      privacy: k,
      notificationSettings: w,
      customization: d,
      verifyAuthPath: R,
      updatePasswordPath: g,
      resendAuthPath: S,
      successAuthPath: b,
      signinAuthPath: x,
      resetAuthPath: y,
      disableStyles: P,
      messages: N,
      ...$
    }, q = new window.AuthiqaWidget(Y);
    v.current = q, q.show(E);
    const I = (T) => {
      n && n(T);
    }, _ = (T) => {
      u && u(T);
    };
    return n && document.addEventListener("authiqa:success", I), u && document.addEventListener("authiqa:error", _), () => {
      n && document.removeEventListener("authiqa:success", I), u && document.removeEventListener("authiqa:error", _), v.current && typeof v.current.destroy == "function" && v.current.destroy();
    };
  }, [
    i,
    E,
    l,
    f,
    n,
    u,
    d,
    h,
    j,
    k,
    w,
    R,
    g,
    S,
    b,
    x,
    y,
    P,
    N
  ]), /* @__PURE__ */ oe.jsx("div", { id: C.current, ref: L });
};
export {
  ce as AuthiqaWidget
};
