# Authiqa Widget Deployment Instructions

## Overview
Both packages have been updated and are ready for deployment:
- Main Widget: v1.0.0 → v1.1.0
- React Package: v1.0.6 → v1.1.0

## Pre-Deployment Checklist
- [x] Security vulnerabilities fixed
- [x] Main widget builds successfully
- [x] React package builds successfully  
- [x] TypeScript definitions generated
- [x] React test environment verified
- [x] Package versions updated

## Deployment Steps

### 1. Deploy Main Widget (@authiqa/widget)

```bash
cd authiqa-widget
npm login  # Login with npm credentials
npm publish
```

### 2. Deploy React Package (authiqa-react)

```bash
cd authiqa-widget/authiqa-react
npm login  # Login with npm credentials
npm publish
```

### 3. CDN Deployment (if applicable)

Upload the following files to your CDN:
- `authiqa-widget/dist/index.js` → `https://widget.authiqa.com/v1.1.0/authiqa-widget.js`
- `authiqa-widget/dist/index.js` → `https://widget.authiqa.com/latest/authiqa-widget.js`

### 4. Update Documentation

Update any documentation that references:
- Package versions
- Installation instructions
- CDN URLs

## Package Details

### Main Widget Package
- **Name**: @authiqa/widget
- **Version**: 1.1.0
- **Main**: dist/index.js
- **Types**: dist/index.d.ts
- **Size**: ~108KB (minified)

### React Package
- **Name**: authiqa-react
- **Version**: 1.1.0
- **Main**: dist/authiqa-react.umd.js
- **Module**: dist/authiqa-react.mjs
- **Types**: dist/index.d.ts
- **Size**: ~11KB (ESM), ~8KB (UMD)

## Verification

After deployment, verify:
1. Packages are available on npm
2. CDN URLs are accessible
3. Test applications can install and use new versions
4. All authentication flows work correctly

## Rollback Plan

If issues are discovered:
1. Unpublish problematic versions (if within 24 hours)
2. Revert CDN files to previous version
3. Update documentation to reference stable version

## Notes

- Both packages maintain backward compatibility
- No breaking changes introduced
- Security vulnerabilities have been addressed
- React package tested with React 19
