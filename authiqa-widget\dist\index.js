!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Authiqa=e():t.Authiqa=e()}(this,(()=>(()=>{"use strict";var t={959:(t,e,n)=>{n.d(e,{A:()=>s});var i=n(601),o=n.n(i),a=n(314),r=n.n(a)()(o());r.push([t.id,'/* Base styles */\nbody {\n    margin: 0;\n    min-height: 100vh;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\nbody[data-theme="dark"] {\n    background-color: #18181b;\n}\n\n.authiqa-container {\n    font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif;\n    max-width: 400px;\n    width: 100%;\n    margin: 2rem;\n    padding: 1.25rem 1.5rem 1.5rem 1.5rem; /* Reduced horizontal padding from 2.5rem to 1.5rem */\n    border-radius: 16px;\n}\n\n.authiqa-container h1 {\n    font-size: 2rem;\n    font-weight: 600;\n    margin-top: 0; /* Added to ensure no extra top margin */\n    margin-bottom: 2rem;\n    color: #1a1a1a;\n    text-align: center;\n}\n\n.authiqa-container form {\n    display: flex;\n    flex-direction: column;\n    gap: 1.5rem;\n}\n\n.authiqa-container label,\n.authiqa-label {\n    font-size: 1rem;\n    font-weight: 400;\n    color: #1a1a1a;\n    margin-bottom: 0.5rem !important; /* Changed to 0.3rem (approximately 5px) */\n    padding-left: 0.09rem !important; /* Added left padding to move labels slightly right */\n    display: block;\n    height: 14px !important; /* Added fixed height */\n    line-height: 14px !important; /* Added line height to match height */\n}\n\n/* Add more spacing between input groups */\n.authiqa-container .authiqa-labeled-input {\n    margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1rem (about 16px) */\n}\n\n.authiqa-container input[type="text"],\n.authiqa-container input[type="email"],\n.authiqa-container input[type="password"] {\n    width: 100%;\n    height: 50px; /* Set height to 64px (14x64) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    border: 1px solid #e5e5e5;\n    border-radius: 4px;\n    background-color: #ffffff;\n    transition: border-color 0.2s ease;\n    box-sizing: border-box;\n}\n\n.authiqa-container input[type="text"]:focus,\n.authiqa-container input[type="email"]:focus,\n.authiqa-container input[type="password"]:focus {\n    outline: none;\n    border-color: #000000;\n}\n\n.authiqa-container input[type="text"]::placeholder,\n.authiqa-container input[type="email"]::placeholder,\n.authiqa-container input[type="password"]::placeholder {\n    color: #a3a3a3;\n}\n\n.authiqa-container .terms-container {\n    display: flex;\n    align-items: flex-start;\n    gap: 0.75rem;\n    margin-bottom: 1rem;\n}\n\n.authiqa-container input[type="checkbox"] {\n    margin-top: 0.25rem; /* Adjusted to align with text */\n    margin-right: 0.5rem; /* Standardized margin */\n    margin-left: 0; /* Reset left margin */\n    position: static; /* Remove relative positioning */\n    top: auto; /* Remove top offset */\n}\n\n.authiqa-container .terms-container label {\n    font-size: 0.875rem;\n    color: #525252;\n    line-height: 1.4;\n    margin-top: 0; /* Ensure no top margin */\n    padding-top: 0; /* Ensure no top padding */\n}\n\n.authiqa-container .terms-container a {\n    color: #000000;\n    text-decoration: none;\n}\n\n.authiqa-container .terms-container a:hover {\n    text-decoration: underline;\n}\n\n.authiqa-container .forgot-password,\n.authiqa-container .alternate-action {\n    color: #ffffff !important;\n    font-size: 0.95rem !important;\n    text-align: left !important;\n}\n.authiqa-container .alternate-action {\n    text-align: center;\n}\n.authiqa-container .forgot-password a,\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n.authiqa-container .forgot-password a:hover,\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Update the button styles to include proper centering */\n.authiqa-container button[type="submit"] {\n    width: 100%;\n    height: 40px; /* Changed to fixed 40px height (approximately 14px) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    font-weight: 500;\n    color: #ffffff;\n    background-color: #18181b;\n    border: none;\n    border-radius: 4px;\n    cursor: pointer;\n    transition: all 0.2s ease;\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    line-height: 1;\n    margin-top: 0.5rem; /* Decreased from 1rem to 0.5rem (about 8px) */\n}\n\n.authiqa-container button[type="submit"]:hover {\n    background-color: #27272a;\n    transform: translateY(-1px);\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n/* Add this new style for the active/clicked state */\n.authiqa-container button[type="submit"]:active {\n    transform: scale(0.98);\n    box-shadow: none;\n    background-color: #000000;\n}\n\n.authiqa-container button[type="submit"]:disabled {\n    background-color: #71717a;\n    cursor: not-allowed;\n    transform: none;\n    box-shadow: none;\n}\n\n/* Loading state styles */\n.authiqa-container button[type="submit"].loading {\n    position: relative;\n    color: transparent !important;\n}\n\n.authiqa-container button[type="submit"].loading::after {\n    content: \'\';\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    top: 50%;\n    left: 50%;\n    margin: -10px 0 0 -10px;\n    border: 2px solid #ffffff;\n    border-radius: 50%;\n    border-left-color: transparent;\n    animation: button-loading-spinner 1s linear infinite;\n}\n\n/* Loading text container */\n.authiqa-container .loading-text {\n    text-align: center;\n    margin-top: 8px;\n    font-size: 0.875rem;\n    color: #525252;\n}\n\n/* Dark theme loading text */\n.authiqa-container[data-theme="dark"] .loading-text {\n    color: #a1a1aa;\n}\n\n@keyframes button-loading-spinner {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* Dark theme button styles */\n.authiqa-container[data-theme="dark"] button[type="submit"] {\n    background-color: #ffffff;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:hover {\n    background-color: #e5e5e5;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:disabled {\n    background-color: #a1a1aa;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"].loading::after {\n    border-color: #18181b;\n    border-left-color: transparent;\n}\n\n/* Center alternate-action and set color for both themes */\n.authiqa-container .alternate-action {\n    text-align: center !important;\n    margin-top: 1.5rem;\n    font-size: 0.95rem;\n    color: #1a1a1a !important; /* Force dark text in light theme */\n}\n\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Dark theme overrides */\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #ffffff !important;\n}\n\n/* Dark theme */\n.authiqa-container[data-theme="dark"] {\n    color: #ffffff;\n    background-color: #27272a;\n}\n\n.authiqa-container[data-theme="dark"] h1 {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] label,\n.authiqa-container[data-theme="dark"] .authiqa-label {\n    color: #ffffff !important; /* Ensure white label text in dark theme */\n    \n}\n\n.authiqa-container[data-theme="dark"] input[type="text"],\n.authiqa-container[data-theme="dark"] input[type="email"],\n.authiqa-container[data-theme="dark"] input[type="password"] {\n    background-color: #18181b;\n    border-color: #3f3f46;\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]:focus,\n.authiqa-container[data-theme="dark"] input[type="email"]:focus,\n.authiqa-container[data-theme="dark"] input[type="password"]:focus {\n    border-color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="email"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="password"]::placeholder {\n    color: #71717a;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container label {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container a,\n.authiqa-container[data-theme="dark"] .forgot-password a {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action a {\n    color: #ffffff;\n}\n\n.authiqa-container .password-field-container {\n    position: relative;\n    width: 100%;\n}\n\n.authiqa-container .password-toggle {\n    position: absolute;\n    right: 12px;\n    top: 50%;\n    transform: translateY(-50%);\n    border: none;\n    background: none;\n    cursor: pointer;\n    padding: 8px;\n    color: #71717a;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: color 0.2s ease;\n}\n\n.authiqa-container .password-toggle:hover {\n    color: #000000;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle:hover {\n    color: #ffffff;\n}\n\n/* Message styles */\n.authiqa-message {\n    position: fixed;\n    top: 20px;\n    left: 50%;\n    transform: translateX(-50%);\n    z-index: 1000;\n    padding: 1rem 2rem;\n    border-radius: 0.375rem;\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n    animation: slideDown 0.3s ease-out;\n    max-width: 90%;\n    width: auto;\n    opacity: 0;\n    pointer-events: none;\n    transition: opacity 0.3s;\n}\n\n.authiqa-message.show {\n    opacity: 1;\n    pointer-events: auto;\n}\n\n.authiqa-message.success {\n    background-color: #dcfce7;\n    color: #15803d;\n    border-left: 4px solid #22c55e;\n}\n\n.authiqa-message.error {\n    background-color: #fee2e2;\n    color: #b91c1c;\n    border-left: 4px solid #ef4444;\n}\n\n@keyframes slideDown {\n    from {\n        transform: translate(-50%, -100%);\n        opacity: 0;\n    }\n    to {\n        transform: translate(-50%, 0);\n        opacity: 1;\n    }\n}\n\n/* Dark theme message styles */\nbody[data-theme="dark"] .authiqa-message.success {\n    background-color: #064e3b;\n    color: #ffffff;\n    border-left-color: #059669;\n}\n\nbody[data-theme="dark"] .authiqa-message.error {\n    background-color: #7f1d1d;\n    color: #ffffff;\n    border-left-color: #dc2626;\n}\n\n/* Password validation styling */\n.authiqa-container .password-validation-container {\n    display: grid;\n    grid-template-columns: 1fr 1fr; /* Two columns instead of flex-wrap */\n    gap: 0.5rem;\n    margin-top: 0.5rem;\n}\n\n.authiqa-container .validation-item {\n    display: flex;\n    align-items: center;\n    font-size: 0.75rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-dot {\n    margin-right: 0.25rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-item.valid .validation-dot,\n.authiqa-container .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Dark theme adjustments */\n.authiqa-container[data-theme="dark"] .validation-item {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-dot,\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Google button container - minimal styling to maintain layout */\n#google-button-container, #google-signup-button-container {\n    width: 100%;\n}\n',""]);const s=r},314:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",i=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),i&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),i&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,i,o,a){"string"==typeof t&&(t=[[null,t,void 0]]);var r={};if(i)for(var s=0;s<this.length;s++){var c=this[s][0];null!=c&&(r[c]=!0)}for(var u=0;u<t.length;u++){var d=[].concat(t[u]);i&&r[d[0]]||(void 0!==a&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=a),n&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=n):d[2]=n),o&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=o):d[4]="".concat(o)),e.push(d))}},e}},601:t=>{t.exports=function(t){return t[1]}},790:(t,e,n)=>{n.r(e),n.d(e,{default:()=>v});var i=n(72),o=n.n(i),a=n(825),r=n.n(a),s=n(659),c=n.n(s),u=n(56),d=n.n(u),l=n(540),h=n.n(l),p=n(494),m=n.n(p),g=n(959),f={};f.styleTagTransform=m(),f.setAttributes=d(),f.insert=c().bind(null,"head"),f.domAPI=r(),f.insertStyleElement=h(),o()(g.A,f);const v=g.A&&g.A.locals?g.A.locals:void 0},72:t=>{var e=[];function n(t){for(var n=-1,i=0;i<e.length;i++)if(e[i].identifier===t){n=i;break}return n}function i(t,i){for(var a={},r=[],s=0;s<t.length;s++){var c=t[s],u=i.base?c[0]+i.base:c[0],d=a[u]||0,l="".concat(u," ").concat(d);a[u]=d+1;var h=n(l),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==h)e[h].references++,e[h].updater(p);else{var m=o(p,i);i.byIndex=s,e.splice(s,0,{identifier:l,updater:m,references:1})}r.push(l)}return r}function o(t,e){var n=e.domAPI(e);return n.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,o){var a=i(t=t||[],o=o||{});return function(t){t=t||[];for(var r=0;r<a.length;r++){var s=n(a[r]);e[s].references--}for(var c=i(t,o),u=0;u<a.length;u++){var d=n(a[u]);0===e[d].references&&(e[d].updater(),e.splice(d,1))}a=c}}},659:t=>{var e={};t.exports=function(t,n){var i=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(n)}},540:t=>{t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},56:(t,e,n)=>{t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},825:t=>{t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var i="";n.supports&&(i+="@supports (".concat(n.supports,") {")),n.media&&(i+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(i+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),i+=n.css,o&&(i+="}"),n.media&&(i+="}"),n.supports&&(i+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleTagTransform(i,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},494:t=>{t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},156:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},o=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(o,a){function r(t){try{c(i.next(t))}catch(t){a(t)}}function s(t){try{c(i.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,i,o,a,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((o=(o=r.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){r.label=s[1];break}if(6===s[0]&&r.label<o[1]){r.label=o[1],o=s;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(s);break}o[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.AuthiqaWidget=void 0;var r=n(752),s=n(92),c=n(113);n(790);var u=function(){function t(t){this.authUrls=null,this.currentAction=null,this.emailVerificationRequired=!1,window.AuthiqaGlobalConfig&&(window.AuthiqaGlobalConfig.customization&&(t.customization=i(i({},window.AuthiqaGlobalConfig.customization),t.customization)),window.AuthiqaGlobalConfig.messages&&(t.messages=i(i({},window.AuthiqaGlobalConfig.messages),t.messages))),this.config=t,this.api=new r.ApiService(t),this.injectStyles()}return t.prototype.storeTokens=function(t){"string"==typeof t?(localStorage.setItem("access_token",t),sessionStorage.setItem("refresh_token",t)):t.accessToken&&t.refreshToken&&(localStorage.setItem("access_token",t.accessToken),sessionStorage.setItem("refresh_token",t.refreshToken))},t.prototype.isUserAuthenticated=function(){var t=localStorage.getItem("access_token"),e=sessionStorage.getItem("refresh_token");return!(!t&&!e)},t.prototype.getStoredToken=function(){return sessionStorage.getItem("refresh_token")||localStorage.getItem("access_token")},t.prototype.getAuthUrls=function(){if(!this.authUrls)throw new Error("Widget not initialized. Call initialize() first.");return this.authUrls},t.prototype.initialize=function(){var t,e;return o(this,void 0,void 0,(function(){var n,i,o;return a(this,(function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.api.getOrganizationDetails()];case 1:return n=a.sent(),this.authUrls=n.authUrls,n.googleSsoConfig&&(this.googleSsoConfig=n.googleSsoConfig),n.githubSsoConfig&&(this.githubSsoConfig=n.githubSsoConfig),this.emailVerificationRequired=null!==(t=n.emailVerificationRequired)&&void 0!==t&&t,n.jwtSecret&&localStorage.setItem("jwt_secret",n.jwtSecret),null!==(e=n.domainRestrictionEnabled)&&void 0!==e&&!e||this.validateDomain(n.organizationUrl)?("signin"===this.currentAction&&this.renderSignInForm(),[3,3]):(this.showUnauthorizedError(),[2]);case 2:return i=a.sent(),console.warn("Failed to fetch organization details:",i),o=this.api.getApiBase(),this.authUrls={signin:"".concat(o,"/auth/signin"),signup:"".concat(o,"/auth/signup"),verify:"".concat(o,"/auth/verify"),reset:"".concat(o,"/auth/reset"),update:"".concat(o,"/auth/update"),resend:"".concat(o,"/auth/resend"),successful:"".concat(o,"/auth/successful")},[3,3];case 3:return[2]}}))}))},t.prototype.show=function(t){this.currentAction&&this.currentAction!==t&&this.dismissGoogleOneTap(),this.currentAction=t,"verify"===t?this.handleEmailVerification():"signin"===t?this.renderSignInForm():"signup"===t?this.renderSignUpForm():"reset"===t?this.renderResetPasswordForm():"update"===t?this.renderUpdatePasswordForm():"resend"===t&&this.renderResendConfirmationForm(),this.authUrls||this.initialize().catch((function(t){console.warn("Failed to fetch organization details:",t)}))},t.prototype.initializeContainer=function(){var t,e=document.getElementById(this.config.container);if(e||((e=document.createElement("div")).id=this.config.container,document.body.appendChild(e)),e.className="authiqa-container",null===(t=this.config.customization)||void 0===t?void 0:t.pageLayout){var n=this.config.customization.pageLayout;if(n.formPosition)switch(document.body.style.display="flex",document.body.style.minHeight="100vh",n.formPosition){case"top":document.body.style.alignItems="flex-start",document.body.style.justifyContent="center";break;case"bottom":document.body.style.alignItems="flex-end",document.body.style.justifyContent="center";break;case"left":document.body.style.alignItems="center",document.body.style.justifyContent="flex-start";break;case"right":document.body.style.alignItems="center",document.body.style.justifyContent="flex-end";break;default:document.body.style.alignItems="center",document.body.style.justifyContent="center"}n.formMarginTop&&(e.style.marginTop=n.formMarginTop),n.formMarginBottom&&(e.style.marginBottom=n.formMarginBottom),n.formMarginLeft&&(e.style.marginLeft=n.formMarginLeft),n.formMarginRight&&(e.style.marginRight=n.formMarginRight)}return this.config.customization||this.config.disableStyles||("dark"===this.config.theme?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme"),"none"!==this.config.theme&&e.setAttribute("data-theme",this.config.theme||"light")),e},t.prototype.createLabeledInput=function(t,e,n,i,o){void 0===o&&(o=!0);var a=document.createElement("div");a.className="labeled-input-container",a.classList.add("authiqa-labeled-input");var r=document.createElement("label");r.setAttribute("for","authiqa-".concat(e)),r.textContent=i,r.classList.add("authiqa-label");var s=document.createElement("input");return s.setAttribute("type",t),s.setAttribute("id","authiqa-".concat(e)),s.setAttribute("name",e),s.setAttribute("placeholder",n),s.setAttribute("required",o?"true":"false"),s.classList.add("authiqa-input"),"password"===t&&s.setAttribute("minlength","6"),a.appendChild(r),a.appendChild(s),{container:a,input:s}},t.prototype.createPasswordField=function(t,e,n){var i=document.createElement("div");if(i.classList.add("authiqa-labeled-input"),n){var o=document.createElement("label");o.setAttribute("for","authiqa-".concat(e)),o.textContent=n,o.classList.add("authiqa-label"),i.appendChild(o)}var a=document.createElement("div");a.className="password-field-container",a.classList.add("authiqa-password-container");var r=document.createElement("input");r.setAttribute("type","password"),r.setAttribute("id","authiqa-".concat(e)),r.setAttribute("name",e),r.setAttribute("placeholder",t),r.setAttribute("required","true"),r.setAttribute("minlength","6"),r.classList.add("authiqa-input"),a.appendChild(r);var s=document.createElement("button");s.setAttribute("type","button"),s.classList.add("password-toggle"),s.innerHTML="👁️",s.addEventListener("click",(function(){var t=r.getAttribute("type");r.setAttribute("type","password"===t?"text":"password"),s.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),a.appendChild(s);var c=document.createElement("div");c.classList.add("password-validation-container");var u=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];return u.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var i=document.createElement("span");i.classList.add("validation-text"),i.textContent=t.text,e.appendChild(n),e.appendChild(i),c.appendChild(e)})),i.appendChild(a),i.appendChild(c),r.addEventListener("input",(function(){var t=r.value;u.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))})),{container:i,input:r}},t.prototype.renderSignInForm=function(){var t,e,n,i,r,s,c,u,d,l,h,p,m,g,f,v,b,y,w=this,x=this.initializeContainer();x.innerHTML="",window._authiqaGoogleButtonRendering=!1;var S=document.createElement("h1");S.classList.add("authiqa-title"),S.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.signinText)||"Sign in",x.appendChild(S);var A=document.createElement("form");A.classList.add("authiqa-form"),A.style.display="flex",A.style.flexDirection="column",A.style.gap="1rem";var q=this.createLabeledInput("email","email",(null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.inputs)||void 0===r?void 0:r.emailPlaceholder)||"Email Address",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.emailLabel)||"Email"),k=q.container,C=q.input,T=this.createPasswordField((null===(d=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===d?void 0:d.passwordPlaceholder)||"Password","password",(null===(h=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===h?void 0:h.passwordLabel)||"Password"),P=T.container,E=T.input,L=this.config.resetAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.reset)||"#",I=null===(m=this.config.customization)||void 0===m?void 0:m.navLinks,_=(null==I?void 0:I.forgotPrompt)||"Forgot Password?",z=(null==I?void 0:I.forgotLinkText)||"Reset",M=document.createElement("div");M.className="forgot-password",M.innerHTML="".concat(_,' <a href="').concat(L,'">').concat(z,"</a>");var O=document.createElement("button");O.setAttribute("type","submit"),O.classList.add("authiqa-button"),O.textContent=(null===(f=null===(g=this.config.customization)||void 0===g?void 0:g.buttons)||void 0===f?void 0:f.signinText)||"Sign In",O.style.marginTop="0.5rem",A.appendChild(k),A.appendChild(P),A.appendChild(M),A.appendChild(O),A.addEventListener("submit",(function(t){return o(w,void 0,void 0,(function(){var e,n,i,o,r,s,c,u,d,l,h,p,m,g,f,v,b,y,w=this;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),O.setAttribute("data-original-text",O.textContent||"Submit"),this.setLoadingState(O,!0,"signin"),e={email:C.value,password:E.value,parentPublicKey:this.config.publicKey},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signin"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&"data"in i&&(this.storeTokens(i.data.token),i.data.user&&i.data.user.publicKey&&sessionStorage.setItem("publicKey",i.data.user.publicKey),i.data.user&&i.data.user.email&&sessionStorage.setItem("user_email",i.data.user.email),i.data.jwtSecret&&localStorage.setItem("jwt_secret",i.data.jwtSecret),(null===(h=i.data.passwordStatus)||void 0===h?void 0:h.expired)?(r=this.config.resetAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.reset)||"",this.showMessage("Your password has expired. Please update it now.","warning",r)):void 0!==(null===(m=i.data.passwordStatus)||void 0===m?void 0:m.daysUntilExpiry)&&i.data.passwordStatus.daysUntilExpiry<=14?(o=i.data.passwordStatus.daysUntilExpiry,r=this.config.resetAuthPath||(null===(g=this.authUrls)||void 0===g?void 0:g.reset)||"",s="Your password will expire in ".concat(o," day").concat(1!==o?"s":"",". Please update it soon."),o<=3?(this.showMessage(s,"warning"),setTimeout((function(){var t,e=w.config.successAuthPath||(null===(t=w.authUrls)||void 0===t?void 0:t.successful)||"";window.location.href=e}),3e3)):(c=this.config.successAuthPath||(null===(f=this.authUrls)||void 0===f?void 0:f.successful)||"",this.showMessage(s,"warning",c))):(u=this.config.successAuthPath||(null===(v=this.authUrls)||void 0===v?void 0:v.successful)||"",this.dismissGoogleOneTap(),this.showMessage((null===(b=this.config.messages)||void 0===b?void 0:b.signinSuccess)||"Welcome back!","success",u))):!i.success&&"error"in i?"EMAIL_NOT_VERIFIED"===i.error.code&&this.emailVerificationRequired?(d=this.config.resendAuthPath||(null===(y=this.authUrls)||void 0===y?void 0:y.resend)||"",this.showMessage("".concat(i.error.message," Please verify your email before signing in."),"error",d)):this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return l=a.sent(),console.error("Signin network error:",l),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(O,!1,"signin"),[7];case 6:return[2]}}))}))})),x.appendChild(A);var R=function(){var t;if((null===(t=w.githubSsoConfig)||void 0===t?void 0:t.enabled)&&w.githubSsoConfig.clientId&&!document.getElementById("github-button-container")){var e=document.createElement("div");e.id="github-button-container",e.style.margin="0.5rem 0 0 0",e.style.display="flex",e.style.justifyContent="center";var n=document.createElement("button");n.type="button",n.className="authiqa-github-button",n.innerHTML='<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" style="margin-right: 0.5rem;"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Sign in with GitHub',n.onclick=function(){var t=w.githubSsoConfig.clientId,e=w.config.signinAuthPath?w.config.signinAuthPath.startsWith("http")?w.config.signinAuthPath:window.location.origin+w.config.signinAuthPath:window.location.origin+window.location.pathname,n={source:"signin",random:Math.random().toString(36).substring(2,15)},i=encodeURIComponent(JSON.stringify(n));sessionStorage.setItem("github_oauth_state",n.random);var o="https://github.com/login/oauth/authorize?client_id=".concat(encodeURIComponent(t),"&redirect_uri=").concat(encodeURIComponent(e),"&scope=").concat(encodeURIComponent("read:user user:email"),"&state=").concat(encodeURIComponent(i));window.location.href=o},e.appendChild(n),A.insertBefore(e,O.nextSibling)}};if(R(),!(null===(v=this.githubSsoConfig)||void 0===v?void 0:v.enabled)||!this.githubSsoConfig.clientId){var j=setInterval((function(){var t;(null===(t=w.githubSsoConfig)||void 0===t?void 0:t.enabled)&&w.githubSsoConfig.clientId&&!document.getElementById("github-button-container")&&(R(),clearInterval(j))}),200);setTimeout((function(){return clearInterval(j)}),5e3)}var U=function(){var t,e,n,i;if((null===(t=w.googleSsoConfig)||void 0===t?void 0:t.enabled)&&w.googleSsoConfig.clientId&&!document.getElementById("google-button-container")){if((null===(i=null===(n=null===(e=window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.id)||void 0===i?void 0:i.cancel)&&window.google.accounts.id.cancel(),window._authiqaGoogleButtonRendering)return;if(window._authiqaGoogleButtonRendering=!0,!document.getElementById("google-identity-services")){var r=document.createElement("script");r.src="https://accounts.google.com/gsi/client",r.async=!0,r.defer=!0,r.id="google-identity-services",document.head.appendChild(r)}void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),void 0===window._authiqaGoogleOneTapSuccessfulAuth&&(window._authiqaGoogleOneTapSuccessfulAuth=!1);var s=function(){if(window.google&&window.google.accounts){window.google.accounts.id.initialize({client_id:w.googleSsoConfig.clientId,callback:function(t){return o(w,void 0,void 0,(function(){var e,n,i,o,r,s,c,u;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];n="".concat(this.api.getApiBase(),"/auth/google"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(i=a.sent()).text()];case 3:o=a.sent(),r=void 0;try{r=JSON.parse(o)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===i.status&&r.success?(r.token&&this.storeTokens(r.token),r.user&&r.user.publicKey&&sessionStorage.setItem("publicKey",r.user.publicKey),r.user&&r.user.email&&sessionStorage.setItem("user_email",r.user.email),r.jwtSecret?localStorage.setItem("jwt_secret",r.jwtSecret):r.data&&r.data.jwtSecret&&localStorage.setItem("jwt_secret",r.data.jwtSecret),s=this.config.successAuthPath||(null===(c=this.authUrls)||void 0===c?void 0:c.successful)||"/",this.dismissGoogleOneTap(),setTimeout((function(){window.location.href=s}),150)):this.showMessage((null===(u=r.error)||void 0===u?void 0:u.message)||"Google sign-in failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-in","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup",auto_select:!1,use_fedcm_for_button:!0,context:"signin",button_auto_select:!1});var t=document.createElement("div");t.id="google-button-container",t.style.margin="0.5rem 0 0 0",t.style.direction="ltr";var e=document.getElementById("github-button-container");e&&e.parentNode?e.parentNode.insertBefore(t,e.nextSibling):A.insertBefore(t,O.nextSibling),setTimeout((function(){var e=t.clientWidth;window.google.accounts.id.renderButton(t,{theme:"outline",size:"large",text:"continue_with",shape:"rectangular",logo_alignment:"left",width:e,auto_prompt:!1,auto_select:!1,type:"standard"}),setTimeout((function(){var t=document.querySelector(".nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb");t&&(t.style.display="flex",t.style.flexDirection="row-reverse");var e=document.querySelector(".nsm7Bb-HzV7m-LgbsSe-Bz112c");e&&(e.style.marginRight="10px"),window._authiqaGoogleButtonRendering=!1}),200)}),0),setTimeout((function(){!w.shouldEnableGoogleOneTap()||window._authiqaGoogleOneTapDismissed||w.hasGoogleOneTapSuccessfulAuth()||(window.google.accounts.id.prompt((function(t){t.isSkippedMoment()?window._authiqaGoogleOneTapDismissed=!0:t.isDismissedMoment()&&(window._authiqaGoogleOneTapDismissed=!0,"credential_returned"===t.getDismissedReason()&&w.markGoogleOneTapSuccessful())})),window._authiqaGoogleOneTapDismissed=!0)}),1500)}else setTimeout(s,100)};try{s()}catch(t){console.error("Error initializing Google services:",t),window._authiqaGoogleButtonRendering=!1}}};if(U(),!(null===(b=this.googleSsoConfig)||void 0===b?void 0:b.enabled)||!this.googleSsoConfig.clientId){var B=setInterval((function(){var t;(null===(t=w.googleSsoConfig)||void 0===t?void 0:t.enabled)&&w.googleSsoConfig.clientId&&!document.getElementById("google-button-container")&&(U(),clearInterval(B))}),200);setTimeout((function(){return clearInterval(B)}),5e3)}!function(){var t=new URLSearchParams(window.location.search),e=t.get("code"),n=t.get("state"),i=sessionStorage.getItem("github_oauth_state"),r=null;try{r=n?JSON.parse(decodeURIComponent(n)):null}catch(t){r={source:"signin",random:n}}if(e&&n&&i&&r&&r.random===i){var s=new URL(window.location.href);s.searchParams.delete("code"),s.searchParams.delete("state"),window.history.replaceState({},document.title,s.pathname+s.search),o(w,void 0,void 0,(function(){var t,n,i,o,r,s,c,u;return a(this,(function(a){switch(a.label){case 0:this.setLoadingState(O,!0,"signin"),a.label=1;case 1:return a.trys.push([1,4,5,6]),t="".concat(this.api.getApiBase(),"/auth/github"),[4,fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).text()];case 3:i=a.sent(),o=void 0;try{o=JSON.parse(i)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===n.status&&o.success?(o.token&&this.storeTokens(o.token),o.user&&o.user.publicKey&&sessionStorage.setItem("publicKey",o.user.publicKey),o.user&&o.user.email&&sessionStorage.setItem("user_email",o.user.email),o.jwtSecret?localStorage.setItem("jwt_secret",o.jwtSecret):o.data&&o.data.jwtSecret&&localStorage.setItem("jwt_secret",o.data.jwtSecret),r=this.config.successAuthPath||(null===(s=this.authUrls)||void 0===s?void 0:s.successful)||"/",this.showMessage((null===(c=this.config.messages)||void 0===c?void 0:c.signinSuccess)||"Welcome back!","success",r)):this.showMessage((null===(u=o.error)||void 0===u?void 0:u.message)||"GitHub sign-in failed","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error during GitHub sign-in","error"),[3,6];case 5:return this.setLoadingState(O,!1,"signin"),sessionStorage.removeItem("github_oauth_state"),[7];case 6:return[2]}}))}))}}();var N=this.config.signupAuthPath||(null===(y=this.authUrls)||void 0===y?void 0:y.signup)||"#",D=(null==I?void 0:I.signupPrompt)||"Don't have an account?",G=(null==I?void 0:I.signupLinkText)||"Sign Up",H=document.createElement("div");H.className="alternate-action",H.innerHTML="".concat(D,' <a href="').concat(N,'">').concat(G,"</a>"),A.appendChild(H)},t.prototype.renderSignUpForm=function(){var t,e,n,i,r,s,c,u,d,l,h,p,m,g,f,v,b,y,w,x,S,A,q,k=this,C=this.initializeContainer();C.innerHTML="",window._authiqaGoogleSignupButtonRendering=!1;var T=null===(t=this.config.customization)||void 0===t?void 0:t.navLinks,P=document.createElement("h1");P.classList.add("authiqa-title"),P.textContent=(null===(i=null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.typography)||void 0===n?void 0:n.titleText)||void 0===i?void 0:i.signupText)||"Sign up",C.appendChild(P);var E=document.createElement("form");E.classList.add("authiqa-form"),E.style.display="flex",E.style.flexDirection="column",E.style.gap="1rem";var L=this.createLabeledInput("text","username",(null===(s=null===(r=this.config.customization)||void 0===r?void 0:r.inputs)||void 0===s?void 0:s.usernamePlaceholder)||"Username",(null===(u=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===u?void 0:u.usernameLabel)||"Username"),I=L.container,_=L.input,z=this.createLabeledInput("email","email",(null===(l=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===l?void 0:l.emailPlaceholder)||"Email Address",(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.inputs)||void 0===p?void 0:p.emailLabel)||"Email"),M=z.container,O=z.input,R=this.createPasswordField((null===(g=null===(m=this.config.customization)||void 0===m?void 0:m.inputs)||void 0===g?void 0:g.passwordPlaceholder)||"Password","password",(null===(v=null===(f=this.config.customization)||void 0===f?void 0:f.inputs)||void 0===v?void 0:v.passwordLabel)||"Password"),j=R.container,U=R.input;E.appendChild(I),E.appendChild(M),E.appendChild(j);var B=document.createElement("div");B.classList.add("terms-container"),B.style.display="flex",B.style.alignItems="flex-start",B.style.marginBottom="1rem";var N=document.createElement("input");N.setAttribute("type","checkbox"),N.setAttribute("id","terms"),N.setAttribute("name","terms"),N.setAttribute("required","required"),N.style.marginTop="0.25rem",N.style.marginRight="0.5rem";var D=document.createElement("label");D.setAttribute("for","terms"),D.style.flex="1",D.style.margin="0",D.style.padding="0",D.style.color="#525252",D.style.fontSize="0.875rem",D.style.lineHeight="1.4";var G=(null===(y=null===(b=this.config.customization)||void 0===b?void 0:b.typography)||void 0===y?void 0:y.termsText)||{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"}},H=G.agreePrefix,V=G.andConnector,F=G.defaultPrefix,K=G.linkText;D.innerHTML="".concat(H,' <a href="').concat(this.config.termsAndConditions||"#",'">').concat(K.terms,'</a> <a href="').concat(this.config.privacy||"#",'">').concat(K.privacy,"</a> ").concat(V," ").concat(F,' <a href="').concat(this.config.notificationSettings||"#",'">').concat(K.notifications,"</a>."),B.appendChild(N),B.appendChild(D);var W=document.createElement("button");W.setAttribute("type","submit"),W.classList.add("authiqa-button");var J=(null===(x=null===(w=this.config.customization)||void 0===w?void 0:w.buttons)||void 0===x?void 0:x.signupText)||"Create Account";W.textContent=J,E.appendChild(B),E.appendChild(W),E.addEventListener("submit",(function(t){return o(k,void 0,void 0,(function(){var e,n,i,o,r,s,c,u,d,l,h,p,m,g,f;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),!N.checked)return this.showMessage("Please accept the terms and conditions","error"),[2];if(!(e=this.validatePassword(U.value)).isValid&&e.error)return this.showMessage("".concat(e.error.message," (").concat(e.error.code,")"),"error"),[2];W.setAttribute("data-original-text",W.textContent||"Submit"),this.setLoadingState(W,!0,"signup"),n={username:_.value,email:O.value,password:U.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})];case 2:return[4,(i=a.sent()).json()];case 3:switch(o=a.sent(),i.status){case 200:o.success&&o.data&&(o.token?this.storeTokens(o.token):o.data&&o.data.token&&this.storeTokens(o.data.token),o.jwtSecret?localStorage.setItem("jwt_secret",o.jwtSecret):o.data&&o.data.jwtSecret&&localStorage.setItem("jwt_secret",o.data.jwtSecret),o.data.data&&o.data.data.publicKey&&sessionStorage.setItem("publicKey",o.data.data.publicKey),o.data.data&&o.data.data.email&&sessionStorage.setItem("user_email",o.data.data.email),r=void 0,s=void 0,this.emailVerificationRequired?(r=this.config.resendAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.resend)||"",s=(null===(m=this.config.messages)||void 0===m?void 0:m.signupSuccess)||"Account created successfully! Please check your email to verify your account before signing in."):(r=this.config.successAuthPath||(null===(g=this.authUrls)||void 0===g?void 0:g.successful)||"",s=(null===(f=this.config.messages)||void 0===f?void 0:f.signinSuccess)||"Successfully signed up! Welcome to your account."),this.dismissGoogleOneTap(),this.showMessage(s,"success",r));break;case 409:switch((c=o.error).code){case"EMAIL_ALREADY_EXISTS":case"USERNAME_ALREADY_EXISTS":case"DUPLICATE_EMAIL_USERNAME_COMBO":this.showMessage("".concat(c.message," (").concat(c.code,")"),"error");break;default:this.showMessage("".concat(c.message),"error")}break;case 400:switch((u=o.error).code){case"MISSING_REQUEST_BODY":case"MISSING_REQUIRED_FIELDS":case"INVALID_EMAIL_FORMAT":case"INVALID_PASSWORD_FORMAT":case"INVALID_USERNAME_FORMAT":case"MISSING_PARENT_PUBLIC_KEY":this.showMessage("".concat(u.message," (").concat(u.code,")"),"error");break;default:this.showMessage("".concat(u.message),"error")}break;case 401:"INVALID_PARENT_PUBLIC_KEY"===(d=o.error).code?this.showMessage("".concat(d.message," (").concat(d.code,")"),"error"):this.showMessage("".concat(d.message),"error");break;case 403:"PARENT_ACCOUNT_INACTIVE"===(l=o.error).code?this.showMessage("".concat(l.message," (").concat(l.code,")"),"error"):this.showMessage("".concat(l.message),"error");break;case 500:this.showMessage("An internal server error occurred. Please try again later.","error");break;default:this.showMessage("An unexpected error occurred. Please try again.","error")}return[3,6];case 4:return h=a.sent(),console.error("Signup network error:",h),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(W,!1,"signup"),[7];case 6:return[2]}}))}))})),C.appendChild(E);var Y=function(){var t;if((null===(t=k.githubSsoConfig)||void 0===t?void 0:t.enabled)&&k.githubSsoConfig.clientId&&!document.getElementById("github-signup-button-container")){var e=document.createElement("div");e.id="github-signup-button-container",e.style.margin="0.5rem 0 0 0",e.style.display="flex",e.style.justifyContent="center";var n=document.createElement("button");n.type="button",n.className="authiqa-github-button",n.innerHTML='<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" style="margin-right: 0.5rem;"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Sign up with GitHub',n.onclick=function(){var t=k.githubSsoConfig.clientId,e=k.config.signinAuthPath?k.config.signinAuthPath.startsWith("http")?k.config.signinAuthPath:window.location.origin+k.config.signinAuthPath:window.location.origin+window.location.pathname,n={source:"signup",random:Math.random().toString(36).substring(2,15)},i=encodeURIComponent(JSON.stringify(n));sessionStorage.setItem("github_oauth_state",n.random);var o="https://github.com/login/oauth/authorize?client_id=".concat(encodeURIComponent(t),"&redirect_uri=").concat(encodeURIComponent(e),"&scope=").concat(encodeURIComponent("read:user user:email"),"&state=").concat(encodeURIComponent(i));window.location.href=o},e.appendChild(n),E.insertBefore(e,W.nextSibling)}};if(Y(),!(null===(S=this.githubSsoConfig)||void 0===S?void 0:S.enabled)||!this.githubSsoConfig.clientId){var X=setInterval((function(){var t;(null===(t=k.githubSsoConfig)||void 0===t?void 0:t.enabled)&&k.githubSsoConfig.clientId&&!document.getElementById("github-signup-button-container")&&(Y(),clearInterval(X))}),200);setTimeout((function(){return clearInterval(X)}),5e3)}var Z=function(){var t,e,n,i;if((null===(t=k.googleSsoConfig)||void 0===t?void 0:t.enabled)&&k.googleSsoConfig.clientId&&!document.getElementById("google-signup-button-container")){if((null===(i=null===(n=null===(e=window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.id)||void 0===i?void 0:i.cancel)&&window.google.accounts.id.cancel(),window._authiqaGoogleSignupButtonRendering)return;if(window._authiqaGoogleSignupButtonRendering=!0,!document.getElementById("google-identity-services")){var r=document.createElement("script");r.src="https://accounts.google.com/gsi/client",r.async=!0,r.defer=!0,r.id="google-identity-services",document.head.appendChild(r)}void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),void 0===window._authiqaGoogleOneTapSuccessfulAuth&&(window._authiqaGoogleOneTapSuccessfulAuth=!1);var s=function(){if(window.google&&window.google.accounts){window.google.accounts.id.initialize({client_id:k.googleSsoConfig.clientId,callback:function(t){return o(k,void 0,void 0,(function(){var e,n,i,o,r,s,c,u;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];n="".concat(this.api.getApiBase(),"/auth/google"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(i=a.sent()).text()];case 3:o=a.sent(),r=void 0;try{r=JSON.parse(o)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===i.status&&r.success?(r.token&&this.storeTokens(r.token),r.user&&r.user.publicKey&&sessionStorage.setItem("publicKey",r.user.publicKey),r.user&&r.user.email&&sessionStorage.setItem("user_email",r.user.email),r.jwtSecret?localStorage.setItem("jwt_secret",r.jwtSecret):r.data&&r.data.jwtSecret&&localStorage.setItem("jwt_secret",r.data.jwtSecret),s=this.config.successAuthPath||(null===(c=this.authUrls)||void 0===c?void 0:c.successful)||"/",this.dismissGoogleOneTap(),setTimeout((function(){window.location.href=s}),150)):this.showMessage((null===(u=r.error)||void 0===u?void 0:u.message)||"Google sign-up failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-up","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup",auto_select:!1,use_fedcm_for_button:!0,context:"signup",button_auto_select:!1});var t=document.createElement("div");t.id="google-signup-button-container",t.style.margin="0.5rem 0 0 0",t.style.direction="ltr";var e=document.getElementById("github-signup-button-container");e&&e.parentNode?e.parentNode.insertBefore(t,e.nextSibling):E.insertBefore(t,W.nextSibling),setTimeout((function(){var e=t.clientWidth;window.google.accounts.id.renderButton(t,{theme:"outline",size:"large",text:"continue_with",shape:"rectangular",logo_alignment:"left",width:e,auto_prompt:!1,auto_select:!1,type:"standard"}),setTimeout((function(){var t=document.querySelector(".nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb");t&&(t.style.display="flex",t.style.flexDirection="row-reverse");var e=document.querySelector(".nsm7Bb-HzV7m-LgbsSe-Bz112c");e&&(e.style.marginRight="10px"),window._authiqaGoogleSignupButtonRendering=!1}),200)}),0),setTimeout((function(){!k.shouldEnableGoogleOneTap()||window._authiqaGoogleOneTapDismissed||k.hasGoogleOneTapSuccessfulAuth()||(window.google.accounts.id.prompt((function(t){t.isSkippedMoment()?window._authiqaGoogleOneTapDismissed=!0:t.isDismissedMoment()&&(window._authiqaGoogleOneTapDismissed=!0,"credential_returned"===t.getDismissedReason()&&k.markGoogleOneTapSuccessful())})),window._authiqaGoogleOneTapDismissed=!0)}),1500)}else setTimeout(s,100)};try{s()}catch(t){console.error("Error initializing Google signup services:",t),window._authiqaGoogleSignupButtonRendering=!1}}};if(Z(),!(null===(A=this.googleSsoConfig)||void 0===A?void 0:A.enabled)||!this.googleSsoConfig.clientId){var $=setInterval((function(){var t;(null===(t=k.googleSsoConfig)||void 0===t?void 0:t.enabled)&&k.googleSsoConfig.clientId&&!document.getElementById("google-signup-button-container")&&(Z(),clearInterval($))}),200);setTimeout((function(){return clearInterval($)}),5e3)}!function(){var t=new URLSearchParams(window.location.search),e=t.get("code"),n=t.get("state"),i=sessionStorage.getItem("github_oauth_state"),r=null;try{r=n?JSON.parse(decodeURIComponent(n)):null}catch(t){r={source:"signup",random:n}}if(e&&n&&i&&r&&r.random===i){var s=new URL(window.location.href);s.searchParams.delete("code"),s.searchParams.delete("state"),window.history.replaceState({},document.title,s.pathname+s.search),o(k,void 0,void 0,(function(){var t,n,i,o,r,s,c,u;return a(this,(function(a){switch(a.label){case 0:this.setLoadingState(W,!0,"signup"),a.label=1;case 1:return a.trys.push([1,4,5,6]),t="".concat(this.api.getApiBase(),"/auth/github"),[4,fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).text()];case 3:i=a.sent(),o=void 0;try{o=JSON.parse(i)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===n.status&&o.success?(o.token&&this.storeTokens(o.token),o.user&&o.user.publicKey&&sessionStorage.setItem("publicKey",o.user.publicKey),o.user&&o.user.email&&sessionStorage.setItem("user_email",o.user.email),o.jwtSecret?localStorage.setItem("jwt_secret",o.jwtSecret):o.data&&o.data.jwtSecret&&localStorage.setItem("jwt_secret",o.data.jwtSecret),r=this.config.successAuthPath||(null===(s=this.authUrls)||void 0===s?void 0:s.successful)||"/",this.showMessage((null===(c=this.config.messages)||void 0===c?void 0:c.signupSuccess)||"Account created successfully!","success",r)):this.showMessage((null===(u=o.error)||void 0===u?void 0:u.message)||"GitHub sign-up failed","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error during GitHub sign-up","error"),[3,6];case 5:return this.setLoadingState(W,!1,"signup"),sessionStorage.removeItem("github_oauth_state"),[7];case 6:return[2]}}))}))}}();var Q=this.config.signinAuthPath||(null===(q=this.authUrls)||void 0===q?void 0:q.signin)||"#",tt=(null==T?void 0:T.signinPrompt)||"Already have an account?",et=(null==T?void 0:T.signinLinkText)||"Sign In",nt=document.createElement("div");nt.className="alternate-action",nt.innerHTML="".concat(tt,' <a href="').concat(Q,'">').concat(et,"</a>"),E.appendChild(nt)},t.prototype.validatePassword=function(t){var e=t.length>=8,n=/[A-Z]/.test(t),i=/[0-9]/.test(t),o=/[!@#$%^&*(),.?":{}|<>]/.test(t);if(!(e&&n&&i&&o)){var a="Password must contain:";return e||(a+=" at least 8 characters,"),n||(a+=" at least one uppercase letter,"),i||(a+=" at least one number,"),o||(a+=" at least one special character,"),{isValid:!1,error:{code:"INVALID_PASSWORD_FORMAT",message:a=a.replace(/,$/,"")}}}return{isValid:!0}},t.prototype.renderResetPasswordForm=function(){var t,e,n,i,r,s,c,u,d,l,h,p,m,g,f=this,v=this.initializeContainer();v.innerHTML="";var b=document.createElement("h1");b.classList.add("authiqa-title"),b.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.resetText)||"Reset Password",(null===(s=null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.typography)||void 0===r?void 0:r.subtitleText)||void 0===s?void 0:s.resetText)&&b.setAttribute("data-subtitle",this.config.customization.typography.subtitleText.resetText),v.appendChild(b);var y=document.createElement("form");y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem";var w=this.createLabeledInput("email","email",(null===(u=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===u?void 0:u.emailPlaceholder)||"Email Address",(null===(l=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===l?void 0:l.emailLabel)||"Email"),x=w.container,S=w.input;y.appendChild(x);var A=document.createElement("button");A.setAttribute("type","submit"),A.classList.add("authiqa-button"),A.textContent=(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.buttons)||void 0===p?void 0:p.resetText)||"Reset Password",y.appendChild(A);var q=this.config.signinAuthPath||(null===(m=this.authUrls)||void 0===m?void 0:m.signin)||"#",k=null===(g=this.config.customization)||void 0===g?void 0:g.navLinks,C=(null==k?void 0:k.backToSigninPrompt)||"Back to Sign In?",T=document.createElement("div");T.className="alternate-action",T.innerHTML="".concat(C,' <a href="').concat(q,'">Sign In</a>'),y.appendChild(T),y.addEventListener("submit",(function(t){return o(f,void 0,void 0,(function(){var e,n,i,o,r;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),this.setLoadingState(A,!0,"reset"),e={email:S.value,parentPublicKey:this.config.publicKey,updatePasswordPath:this.config.updatePasswordPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&i.data&&this.showMessage((null===(r=this.config.messages)||void 0===r?void 0:r.resetSuccess)||i.data.message,"success"):!i.success&&i.error?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return o=a.sent(),console.error("Reset password network error:",o),this.showMessage("Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(A,!1,"reset"),[7];case 6:return[2]}}))}))})),v.appendChild(y)},t.prototype.renderUpdatePasswordForm=function(){var t,e,n,i,r,s,c,u,d,l,h,p,m,g=this,f=this.initializeContainer();f.innerHTML="";var v=document.createElement("h1");v.classList.add("authiqa-title"),v.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.updateText)||"Update Password",f.appendChild(v);var b=new URLSearchParams(window.location.search).get("token"),y=document.createElement("form");if(y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem",b){var w=document.createElement("input");w.setAttribute("type","hidden"),w.setAttribute("name","token"),w.value=b,y.appendChild(w)}else console.warn("No token found in URL - password reset may fail");var x=document.createElement("div");x.classList.add("authiqa-labeled-input");var S=document.createElement("label");S.setAttribute("for","authiqa-newPassword"),S.textContent=(null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.inputs)||void 0===r?void 0:r.passwordLabel)||"New Password",S.classList.add("authiqa-label"),x.appendChild(S);var A=document.createElement("div");A.className="password-field-container",A.classList.add("authiqa-password-container");var q=document.createElement("input");q.setAttribute("type","password"),q.setAttribute("id","authiqa-newPassword"),q.setAttribute("name","newPassword"),q.setAttribute("placeholder",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.passwordPlaceholder)||"New Password"),q.setAttribute("required","true"),q.setAttribute("minlength","6"),q.classList.add("authiqa-input"),A.appendChild(q);var k=document.createElement("button");k.setAttribute("type","button"),k.classList.add("password-toggle"),k.innerHTML="👁️",k.addEventListener("click",(function(){var t=q.getAttribute("type");q.setAttribute("type","password"===t?"text":"password"),k.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),A.appendChild(k),x.appendChild(A);var C=document.createElement("div");C.classList.add("authiqa-labeled-input");var T=document.createElement("label");T.setAttribute("for","authiqa-confirmPassword"),T.textContent=(null===(d=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===d?void 0:d.confirmPasswordLabel)||"Confirm Password",T.classList.add("authiqa-label"),C.appendChild(T);var P=document.createElement("div");P.className="password-field-container",P.classList.add("authiqa-password-container");var E=document.createElement("input");E.setAttribute("type","password"),E.setAttribute("id","authiqa-confirmPassword"),E.setAttribute("name","confirmPassword"),E.setAttribute("placeholder",(null===(h=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===h?void 0:h.confirmPasswordPlaceholder)||"Confirm Password"),E.setAttribute("required","true"),E.classList.add("authiqa-input"),P.appendChild(E);var L=document.createElement("button");L.setAttribute("type","button"),L.classList.add("password-toggle"),L.innerHTML="👁️",L.addEventListener("click",(function(){var t=E.getAttribute("type");E.setAttribute("type","password"===t?"text":"password"),L.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),P.appendChild(L),C.appendChild(P);var I=document.createElement("div");I.classList.add("password-validation-container");var _=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];_.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var i=document.createElement("span");i.classList.add("validation-text"),i.textContent=t.text,e.appendChild(n),e.appendChild(i),I.appendChild(e)})),y.appendChild(x),y.appendChild(C),y.appendChild(I),q.addEventListener("input",(function(){var t=q.value;_.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))}));var z=function(){E.value&&(q.value!==E.value?E.setCustomValidity("Passwords do not match"):E.setCustomValidity(""))};q.addEventListener("input",z),E.addEventListener("input",z);var M=document.createElement("button");M.setAttribute("type","submit"),M.classList.add("authiqa-button"),M.textContent=(null===(m=null===(p=this.config.customization)||void 0===p?void 0:p.buttons)||void 0===m?void 0:m.updateText)||"Update Password",y.appendChild(M),y.addEventListener("submit",(function(t){return o(g,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),q.value!==E.value)return this.showMessage("Passwords do not match","error"),[2];this.setLoadingState(M,!0,"update"),e={token:b,password:q.value},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/update-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&i.data&&(o=this.config.signinAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.signin),this.showMessage((null===(s=this.config.messages)||void 0===s?void 0:s.updateSuccess)||"Password updated successfully!","success",o)):!i.success&&i.error?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(M,!1,"update"),[7];case 6:return[2]}}))}))})),f.appendChild(y)},t.prototype.renderResendConfirmationForm=function(){var t=this.initializeContainer();t.innerHTML="",this.isUserAuthenticated()?this.renderSimpleVerifyButton(t):this.renderTraditionalResendForm(t)},t.prototype.renderSimpleVerifyButton=function(t){var e,n,i=document.createElement("button");i.classList.add("authiqa-button"),i.textContent=(null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.buttons)||void 0===n?void 0:n.resendText)||"Verify Email",i.style.width="100%",i.style.maxWidth="300px",i.style.margin="0 auto",i.style.display="block",this.attachResendButtonHandler(i,!0),t.appendChild(i)},t.prototype.renderTraditionalResendForm=function(t){var e,n,i,r,s,c,u,d,l,h,p,m=this,g=document.createElement("h1");g.classList.add("authiqa-title"),g.textContent=(null===(i=null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.typography)||void 0===n?void 0:n.titleText)||void 0===i?void 0:i.resendText)||"Resend Confirmation",t.appendChild(g);var f=document.createElement("form");f.classList.add("authiqa-form"),f.style.display="flex",f.style.flexDirection="column",f.style.gap="1rem";var v=new URLSearchParams(window.location.search).get("email"),b=this.createLabeledInput("email","email",(null===(s=null===(r=this.config.customization)||void 0===r?void 0:r.inputs)||void 0===s?void 0:s.emailPlaceholder)||"Email Address",(null===(u=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===u?void 0:u.emailLabel)||"Email"),y=b.container,w=b.input;v&&(w.value=v),f.appendChild(y);var x=document.createElement("button");x.setAttribute("type","submit"),x.classList.add("authiqa-button"),x.textContent=(null===(l=null===(d=this.config.customization)||void 0===d?void 0:d.buttons)||void 0===l?void 0:l.resendText)||"Verify Email",f.appendChild(x);var S=this.config.signinAuthPath||(null===(h=this.authUrls)||void 0===h?void 0:h.signin)||"#",A=null===(p=this.config.customization)||void 0===p?void 0:p.navLinks,q=(null==A?void 0:A.backToSigninPrompt)||"Back to Sign In?",k=document.createElement("div");k.className="alternate-action",k.innerHTML="".concat(q,' <a href="').concat(S,'">Sign In</a>'),f.appendChild(k),f.addEventListener("submit",(function(t){return o(m,void 0,void 0,(function(){return a(this,(function(e){return t.preventDefault(),this.handleTraditionalResendSubmit(w,x),[2]}))}))})),t.appendChild(f)},t.prototype.attachResendButtonHandler=function(t,e){var n=this;t.addEventListener("click",(function(i){return o(n,void 0,void 0,(function(){var n,o,r,s,c,u,d,l;return a(this,(function(a){switch(a.label){case 0:if(i.preventDefault(),e&&(n=localStorage.getItem("authiqa_last_resend_time"),o=Date.now(),n&&o-parseInt(n)<6e4))return r=Math.ceil((6e4-(o-parseInt(n)))/1e3),this.showMessage("Please wait ".concat(r," seconds before requesting another email."),"error"),[2];this.setLoadingState(t,!0,"resend"),a.label=1;case 1:return a.trys.push([1,4,5,6]),(s=this.getStoredToken())?[4,fetch("".concat(this.api.getApiBase(),"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({verifyAuthPath:this.config.verifyAuthPath})})]:(this.showMessage("Authentication token not found. Please sign in again.","error"),[2]);case 2:return[4,(c=a.sent()).json()];case 3:if(u=a.sent(),200!==c.status||!u.success)throw new Error((null===(l=u.error)||void 0===l?void 0:l.message)||"Failed to send verification email");return this.showMessage("Verification email sent successfully!","success"),localStorage.setItem("authiqa_last_resend_time",Date.now().toString()),[3,6];case 4:return d=a.sent(),this.showMessage(d.message||"An error occurred","error"),[3,6];case 5:return this.setLoadingState(t,!1,"resend"),[7];case 6:return[2]}}))}))}))},t.prototype.handleTraditionalResendSubmit=function(t,e){var n,i,r;return o(this,void 0,void 0,(function(){var o,s,c,u;return a(this,(function(a){switch(a.label){case 0:if(!t||!t.value)return this.showMessage("Please enter your email address.","error"),[2];this.setLoadingState(e,!0,"resend"),a.label=1;case 1:return a.trys.push([1,4,5,6]),o={email:t.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},[4,fetch("".concat(this.api.getApiBase(),"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})];case 2:return[4,(s=a.sent()).json()];case 3:if(c=a.sent(),200!==s.status||!c.success)throw new Error((null===(r=c.error)||void 0===r?void 0:r.message)||"Failed to send verification email");return this.showMessage((null===(n=this.config.messages)||void 0===n?void 0:n.resendSuccess)||(null===(i=c.data)||void 0===i?void 0:i.message)||"Verification email sent successfully!","success"),[3,6];case 4:return u=a.sent(),this.showMessage(u.message||"Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(e,!1,"resend"),[7];case 6:return[2]}}))}))},t.prototype.renderVerificationStatus=function(t,e){var n,i,o=this.initializeContainer();o.innerHTML="";var a=document.createElement("div");a.className="verification-status";var r=document.createElement("h1");r.textContent="Email Verification";var s=document.createElement("div");if("loading"===t){var c=document.createElement("div");c.className="verification-loader",s.appendChild(c),e=(null===(n=this.config.messages)||void 0===n?void 0:n.verificationLoading)||e}else{var u=document.createElement("div");u.className="verification-icon ".concat(t),u.innerHTML="success"===t?"✓":"✕",s.appendChild(u),"success"===t&&(e=(null===(i=this.config.messages)||void 0===i?void 0:i.verificationSuccess)||e)}var d=document.createElement("p");d.textContent=e,a.appendChild(r),a.appendChild(s),a.appendChild(d),o.appendChild(a)},t.prototype.handleEmailVerification=function(){var t,e,n,i;return o(this,void 0,void 0,(function(){var o,r,s,c,u,d,l,h,p;return a(this,(function(a){switch(a.label){case 0:if(o=new URLSearchParams(window.location.search),!(r=o.get("token")))return this.renderVerificationStatus("error","Invalid verification token (INVALID_TOKEN)"),[2];this.renderVerificationStatus("loading",(null===(t=this.config.messages)||void 0===t?void 0:t.verificationLoading)||"Verifying your email address..."),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/confirm-email?token=").concat(encodeURIComponent(r)),{headers:{"X-Public-Key":this.config.publicKey,"Content-Type":"application/json"}})];case 2:return[4,(s=a.sent()).json()];case 3:switch(c=a.sent(),s.status){case 200:c.success&&c.data&&(this.renderVerificationStatus("success",(null===(e=this.config.messages)||void 0===e?void 0:e.verificationSuccess)||c.data.message||"Email verified successfully"),u=this.emailVerificationRequired?this.config.signinAuthPath||(null===(n=this.authUrls)||void 0===n?void 0:n.signin)||"/":this.config.successAuthPath||(null===(i=this.authUrls)||void 0===i?void 0:i.successful)||"/",setTimeout((function(){window.location.href=u}),2e3));break;case 400:d=c.error,this.renderVerificationStatus("error","".concat(d.message," (").concat(d.code,")"));break;case 404:l=c.error,this.renderVerificationStatus("error","".concat(l.message," (").concat(l.code,")"));break;case 500:h=c.error,this.renderVerificationStatus("error","".concat(h.message," (").concat(h.code,")"));break;default:this.renderVerificationStatus("error","An unexpected error occurred. Please try again.")}return[3,5];case 4:return p=a.sent(),console.error("Error during email verification:",p),this.renderVerificationStatus("error","Network error: Unable to connect to the server. Please check your connection and try again."),[3,5];case 5:return[2]}}))}))},t.prototype.showMessage=function(t,e,n){var i,o,a,r=document.createElement("div");r.classList.add("authiqa-message"),r.classList.add("authiqa-message-".concat(e)),"success"===e?r.style.backgroundColor="#4caf50":"error"===e?r.style.backgroundColor="#f44336":"warning"===e&&(r.style.backgroundColor="#ff9800");var s=n;if("success"===e&&!n)switch(this.currentAction){case"signin":s=this.config.successAuthPath||(null===(i=this.authUrls)||void 0===i?void 0:i.successful);break;case"update":s=this.config.signinAuthPath||(null===(o=this.authUrls)||void 0===o?void 0:o.signin);break;case"signup":s=this.config.successAuthPath||(null===(a=this.authUrls)||void 0===a?void 0:a.successful)}var c=document.querySelector(".authiqa-message");c&&c.remove(),r.textContent=t,document.body.appendChild(r),r.classList.add("show");var u=2e3;"error"===e?u=7e3:"success"===e?u=4e3:"warning"===e&&(u=5e3),setTimeout((function(){r.classList.remove("show"),setTimeout((function(){r.remove(),s&&(window.location.href=s)}),300)}),u)},t.prototype.getCustomSuccessMessage=function(t){var e,n,i,o,a;switch(this.currentAction){case"signin":return(null===(e=this.config.messages)||void 0===e?void 0:e.signinSuccess)||t;case"signup":return(null===(n=this.config.messages)||void 0===n?void 0:n.signupSuccess)||"Successfully signed up! Welcome to your account.";case"reset":return(null===(i=this.config.messages)||void 0===i?void 0:i.resetSuccess)||t;case"update":return(null===(o=this.config.messages)||void 0===o?void 0:o.updateSuccess)||t;case"resend":return(null===(a=this.config.messages)||void 0===a?void 0:a.resendSuccess)||t;default:return t}},t.prototype.setLoadingState=function(t,e,n){if(e){var i=t.textContent||"Submit";t.setAttribute("data-original-text",i);var o=this.getCustomLoadingMessage(n)||"Please wait...";t.textContent=o}else t.textContent=t.getAttribute("data-original-text")||"Submit"},t.prototype.getCustomLoadingMessage=function(t){var e,n,i,o,a;switch(t){case"signin":return null===(e=this.config.messages)||void 0===e?void 0:e.signinLoading;case"signup":return null===(n=this.config.messages)||void 0===n?void 0:n.signupLoading;case"reset":return null===(i=this.config.messages)||void 0===i?void 0:i.resetLoading;case"update":return null===(o=this.config.messages)||void 0===o?void 0:o.updateLoading;case"resend":return null===(a=this.config.messages)||void 0===a?void 0:a.resendLoading;default:return}},t.prototype.injectStyles=function(){if(!this.config.disableStyles){var t=document.getElementById("authiqa-styles");t&&t.remove();var e=document.createElement("style");e.id="authiqa-styles";var n=this.config.theme&&"none"!==this.config.theme?this.config.theme:"light",i="";i+=(0,s.getStyleContent)(n);var o=(0,s.getComponentStyles)(n);i+="\n            /* Modal Styles */\n            .authiqa-modal-overlay {\n                ".concat(Object.entries(o.modal.overlay).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-modal-container {\n                ").concat(Object.entries(o.modal.container).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-iframe {\n                ").concat(Object.entries(o.iframe).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            /* Message Styles */\n            .authiqa-message {\n                ").concat(Object.entries(o.message).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-success {\n                ").concat(Object.entries(o.messageSuccess).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-error {\n                ").concat(Object.entries(o.messageError).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-show {\n                ").concat(Object.entries(o.messageShow).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n        "),this.config.customization&&(i+=new c.StyleGenerator(this.config.customization).generateStyles(),i+=(0,s.generateTermsContainerStyles)(this.config)),e.textContent=i,document.head.appendChild(e)}},t.prototype.generateCustomStyles=function(t){var e=t.colors,n=t.typography,i=t.layout,o=t.buttons;return"\n            .authiqa-container {\n                background-color: ".concat(e.background,";\n                padding: ").concat(i.padding,";\n                margin: ").concat(i.margin,";\n                border-radius: ").concat(i.borderRadius,";\n                max-width: ").concat(i.maxWidth,";\n                font-family: ").concat(n.fontFamily,";\n            }\n\n            .authiqa-container h1 {\n                color: ").concat(n.titleColor,";\n                font-size: ").concat(n.titleSize,";\n            }\n\n            .authiqa-container input {\n                background-color: ").concat(e.inputBackground,";\n                color: ").concat(e.inputText,";\n                border: 1px solid ").concat(e.borderColor,";\n            }\n\n            .authiqa-container button {\n                background-color: ").concat(e.buttonBackground,";\n                color: ").concat(e.buttonText,";\n                height: ").concat(o.height||"40px",";\n                width: ").concat(o.width||"100%",";\n                border-radius: ").concat(o.borderRadius,";\n            }\n        ")},t.prototype.updateTheme=function(t){if(!this.config.disableStyles)if(document.getElementById("authiqa-styles")){"dark"===t?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&e.setAttribute("data-theme",t)}else this.injectStyles()},t.prototype.hasGoogleOneTapSuccessfulAuth=function(){try{return"true"===sessionStorage.getItem("authiqa_google_onetap_success")}catch(t){return!0===window._authiqaGoogleOneTapSuccessfulAuth}},t.prototype.markGoogleOneTapSuccessful=function(){try{sessionStorage.setItem("authiqa_google_onetap_success","true")}catch(t){window._authiqaGoogleOneTapSuccessfulAuth=!0}},t.prototype.dismissGoogleOneTap=function(){try{window.google&&window.google.accounts&&window.google.accounts.id&&window.google.accounts.id.cancel(),window._authiqaGoogleOneTapDismissed=!0,this.markGoogleOneTapSuccessful()}catch(t){}},t.prototype.resetGoogleOneTapState=function(){window._authiqaGoogleOneTapDismissed=!1,window._authiqaGoogleOneTapSuccessfulAuth=!1;try{sessionStorage.removeItem("authiqa_google_onetap_success")}catch(t){}},t.prototype.resetGoogleOneTap=function(){this.resetGoogleOneTapState()},t.prototype.cleanup=function(){this.dismissGoogleOneTap();var t=document.getElementById("authiqa-styles");t&&t.remove(),document.body.style.backgroundColor="",document.body.style.display="",document.body.style.minHeight="",document.body.style.alignItems="",document.body.style.justifyContent="",document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&(e.removeAttribute("data-theme"),e.style.marginTop="",e.style.marginBottom="",e.style.marginLeft="",e.style.marginRight="")},t.prototype.handleApiError=function(t){var e;(null===(e=null==t?void 0:t.error)||void 0===e?void 0:e.message)?this.showMessage(t.error.message,"error"):t instanceof Error?this.showMessage("Unable to connect to the server","error"):this.showMessage("An unexpected error occurred","error")},t.prototype.validateDomain=function(t){if(this.isDevelopmentMode())return!0;var e;try{e=new URL(t).hostname}catch(e){return console.error("Invalid organization URL:",t),!1}var n=window.location.hostname;return n===e||n.endsWith("."+e)||"authiqa.com"===n||"www.authiqa.com"===n},t.prototype.isDevelopmentMode=function(){var t=document.querySelector("script[data-public-key]");return!!t&&"true"===t.getAttribute("authiqa--dev-data-mode")},t.prototype.showUnauthorizedError=function(){var t=document.getElementById(this.config.container);if(t){t.innerHTML="";var e=document.createElement("div");e.className="authiqa-error-container";var n=document.createElement("h2");n.textContent="Unauthorized Domain",n.style.color="#e74c3c";var i=document.createElement("p");i.textContent="This widget can only be used on authorized domains. Please visit Authiqa and signin to update your organization related information",i.style.color="#333333";var o=document.createElement("a");o.href="https://authiqa.com",o.textContent="Visit Authiqa",o.style.color="#3498db",e.appendChild(n),e.appendChild(i),e.appendChild(o),t.appendChild(e);var a=document.createElement("style");a.textContent='\n            .authiqa-error-container {\n                padding: 20px;\n                border: 1px solid #e74c3c;\n                border-radius: 5px;\n                background-color: #fef5f5;\n                text-align: center;\n                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;\n            }\n            .authiqa-error-container h2 {\n                color: #e74c3c;\n                margin-top: 0;\n            }\n            .authiqa-error-container p {\n                color: #333333;\n                margin-bottom: 15px;\n            }\n            .authiqa-error-container a {\n                display: inline-block;\n                margin-top: 15px;\n                color: #3498db;\n                text-decoration: none;\n            }\n            .authiqa-error-container a:hover {\n                text-decoration: underline;\n            }\n        ',document.head.appendChild(a)}},t.prototype.shouldEnableGoogleOneTap=function(){return!1!==this.config.enableGoogleOneTap&&("signin"===this.currentAction||"signup"===this.currentAction)},t}();e.AuthiqaWidget=u,window.AuthiqaWidget=u,document.addEventListener("DOMContentLoaded",(function(){try{var t=document.querySelector("script[data-public-key]");if(!t)return void console.error("Script tag with data-public-key not found.");var e=t.getAttribute("data-public-key"),n=t.getAttribute("action");if(!n){var i=window.location.href;n=["signin","signup","verify","reset","update","resend"].find((function(t){return i.includes(t)}))||"signin"}var o=t.getAttribute("termsAndConditions"),a=t.getAttribute("privacy"),r=t.getAttribute("notificationSettings"),s=t.getAttribute("theme")||"light",c="true"===t.getAttribute("disable-styles"),u=t.getAttribute("verifyAuthPath"),d=t.getAttribute("updatePasswordPath"),l=t.getAttribute("resendAuthPath"),h=t.getAttribute("successAuthPath"),p=t.getAttribute("signinAuthPath"),m=t.getAttribute("signupAuthPath"),g=void 0,f=t.getAttribute("data-messages");if(f)try{g=JSON.parse(f)}catch(t){console.error("Failed to parse custom messages:",t)}var v=void 0,b=t.getAttribute("data-customization");if(b)try{v=JSON.parse(b)}catch(t){console.error("Failed to parse customization:",t)}var y="false"!==t.getAttribute("enable-google-one-tap");if("function"!=typeof window.AuthiqaWidget)return void console.error("AuthiqaWidget not properly registered");var w={publicKey:e||"",container:"authiqa",mode:"popup",theme:s,disableStyles:c,organizationDomain:"authiqa.com",enableGoogleOneTap:y,termsAndConditions:o,privacy:a,notificationSettings:r,messages:g,customization:v,verifyAuthPath:u,updatePasswordPath:d,resendAuthPath:l,successAuthPath:h,signinAuthPath:p,signupAuthPath:m};new window.AuthiqaWidget(w).show(n)}catch(t){console.error("Error during widget initialization:",t)}}))},752:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},o=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(o,a){function r(t){try{c(i.next(t))}catch(t){a(t)}}function s(t){try{c(i.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,i,o,a,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((o=(o=r.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){r.label=s[1];break}if(6===s[0]&&r.label<o[1]){r.label=o[1],o=s;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(s);break}o[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.ApiService=void 0;var r=n(616),s=n(733),c=function(){function t(t){this.publicKey=t.publicKey,this.config=(0,r.getApiConfig)(t.organizationDomain),(0,s.validateCustomAuthPaths)({verifyAuthPath:t.verifyAuthPath,updatePasswordPath:t.updatePasswordPath,resendAuthPath:t.resendAuthPath,successAuthPath:t.successAuthPath,signinAuthPath:t.signinAuthPath},t.organizationDomain).isValid&&(this.verifyAuthPath=t.verifyAuthPath,this.updatePasswordPath=t.updatePasswordPath,this.resendAuthPath=t.resendAuthPath,this.successAuthPath=t.successAuthPath,this.signinAuthPath=t.signinAuthPath)}return t.prototype.signup=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resetPassword=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{updatePasswordPath:this.updatePasswordPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resendConfirmation=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.getApiBase=function(){return this.config.API_BASE},t.prototype.getOrganizationDetails=function(){return o(this,void 0,void 0,(function(){var t,e,n,i,o,r;return a(this,(function(a){switch(a.label){case 0:t="".concat(this.config.API_BASE,"/auth/organization-details"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(t,{method:"GET",headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 2:if(!(e=a.sent()).ok)throw new Error("API Error: ".concat(e.statusText));return o=(i=JSON).parse,[4,e.text()];case 3:if((n=o.apply(i,[a.sent()])).success&&n.data)return[2,n.data];throw new Error("Invalid response format from server");case 4:throw r=a.sent(),console.error("Organization Details Request Failed",r),r;case 5:return[2]}}))}))},t.prototype.checkAuthStatus=function(){return o(this,void 0,void 0,(function(){var t;return a(this,(function(e){switch(e.label){case 0:return[4,fetch("".concat(this.config.API_BASE).concat(this.config.ENDPOINTS.AUTH_STATUS),{headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 1:if(!(t=e.sent()).ok)throw new Error("API Error: ".concat(t.statusText));return[2,t.json()]}}))}))},t}();e.ApiService=c},616:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getApiConfig=void 0,e.getApiConfig=function(t){var e;return{API_BASE:"staging"==(((null==(e=document.querySelector("script[data-public-key]"))?void 0:e.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.".concat(t),ENDPOINTS:{ORGANIZATION_DETAILS:"/auth/organization-details",AUTH_STATUS:"/auth/status"}}}},149:(t,e)=>{function n(){var t;return"staging"==(((null==(t=document.querySelector("script[data-public-key]"))?void 0:t.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.authiqa.com"}Object.defineProperty(e,"__esModule",{value:!0}),e.STYLE_CONSTANTS=e.THEMES=e.API_ENDPOINTS=void 0,e.API_ENDPOINTS={ORGANIZATION_DETAILS:"".concat(n(),"/auth/organization-details"),AUTH_STATUS:"".concat(n(),"/auth/status")},e.THEMES={light:{background:"#ffffff",text:"#000000",border:"#e0e0e0",modalOverlay:"rgba(0, 0, 0, 0.5)",labelColor:"#333333"},dark:{background:"#1a1a1a",text:"#ffffff",border:"#333333",modalOverlay:"rgba(0, 0, 0, 0.7)",labelColor:"#ffffff"}},e.STYLE_CONSTANTS={STYLE_ELEMENT_ID:"authiqa-styles",CONTAINER_CLASS:"authiqa-container",THEMES:{LIGHT:"light",DARK:"dark"}}},745:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.defaultCustomization=void 0,e.defaultCustomization={layout:{padding:"1.5rem",paddingTop:"1.25rem",margin:"2rem",borderRadius:"16px",maxWidth:"400px",minWidth:"300px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#000000",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#000000",borderColor:"#e5e5e5"},typography:{titleText:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"2rem",titleColor:"#1a1a1a",labelSize:"0.9rem",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',labelColor:"#333333",labelFontWeight:"400",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.2",termsText:{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#333333",linkColor:"#000000"},navTextColor:"#1a1a1a",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Email Address",passwordPlaceholder:"Password",usernamePlaceholder:"Username",confirmPasswordPlaceholder:"Confirm Password",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm Password",borderRadius:"4px",height:"50px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#000000",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation",height:"40px",width:"100%",borderRadius:"4px",hoverBackground:"#27272a"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign In",signupPrompt:"Don't have an account?",signupLinkText:"Sign Up",forgotPrompt:"Forgot Password?",forgotLinkText:"Reset",fontSize:"0.95rem",color:"#1a1a1a",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#0070f3",linkFontWeight:"500",backToSigninPrompt:"Back to Sign In?"}}},113:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.StyleGenerator=void 0;var o=n(745),a=function(){function t(t){var e,n;this.customization=i(i(i({},o.defaultCustomization),t),{typography:i(i(i({},o.defaultCustomization.typography),null==t?void 0:t.typography),{titleText:i(i({},o.defaultCustomization.typography.titleText),null===(e=null==t?void 0:t.typography)||void 0===e?void 0:e.titleText),subtitleText:i(i({},o.defaultCustomization.typography.subtitleText),null===(n=null==t?void 0:t.typography)||void 0===n?void 0:n.subtitleText)})})}return t.prototype.generateStyles=function(){var t,e,n,i,o,a,r,s,c,u,d,l,h,p,m,g,f,v,b,y,w,x,S,A,q,k,C,T,P,E,L,I,_,z,M,O,R,j=this.customization,U=j.layout,B=j.colors,N=j.typography,D=j.buttons,G=B.background&&("#27272a"===B.background.toLowerCase()||"#18181b"===B.background.toLowerCase());return"\n        .authiqa-container {\n            background-color: ".concat(B.background,";\n            padding: ").concat(U.paddingTop||"1.25rem"," ").concat(U.padding," ").concat(U.padding," ").concat(U.padding,";\n            margin: ").concat(U.margin,";\n            border-radius: ").concat(U.borderRadius,";\n            max-width: ").concat(U.maxWidth,";\n            min-width: ").concat(U.minWidth||"auto",";\n            width: ").concat(U.width||"auto",";\n            height: ").concat(U.height||"auto",";\n            min-height: ").concat(U.minHeight||"auto",";\n            max-height: ").concat(U.maxHeight||"auto",";\n            font-family: ").concat(N.fontFamily,";\n            --authiqa-nav-text-color: ").concat(N.navTextColor||"#1a1a1a",";\n            --authiqa-nav-text-color-dark: ").concat(N.navTextColorDark||"#ffffff",";\n        }\n\n        .authiqa-container h1 {\n            color: ").concat(N.titleColor,";\n            font-size: ").concat(N.titleSize,";\n            margin-top: 0;\n            margin-bottom: 2rem;\n            text-align: ").concat(N.titleAlignment||"center",";\n            font-weight: ").concat(N.titleWeight||"600",";\n            line-height: ").concat(N.titleLineHeight||"1.2",";\n        }\n\n        .authiqa-container input {\n            background-color: ").concat(B.inputBackground,";\n            color: ").concat(B.inputText,";\n            border: 1px solid ").concat(B.borderColor,";\n            border-radius: ").concat((null===(t=this.customization.inputs)||void 0===t?void 0:t.borderRadius)||"4px",";\n            height: ").concat((null===(e=this.customization.inputs)||void 0===e?void 0:e.height)||"50px",";\n            width: ").concat((null===(n=this.customization.inputs)||void 0===n?void 0:n.width)||"100%",";\n            padding: ").concat((null===(i=this.customization.inputs)||void 0===i?void 0:i.padding)||"0 1rem",";\n            margin: ").concat((null===(o=this.customization.inputs)||void 0===o?void 0:o.margin)||"0 0 1rem 0",";\n            font-size: ").concat((null===(a=this.customization.inputs)||void 0===a?void 0:a.fontSize)||"1rem",";\n            font-weight: ").concat((null===(r=this.customization.inputs)||void 0===r?void 0:r.fontWeight)||"400",";\n            box-sizing: border-box;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        .authiqa-container input:focus {\n            border-color: ").concat((null===(s=this.customization.inputs)||void 0===s?void 0:s.focusBorderColor)||"#000000",";\n            box-shadow: ").concat((null===(c=this.customization.inputs)||void 0===c?void 0:c.focusBoxShadow)||"none",';\n            outline: none;\n        }\n\n        /* Make button selectors more specific to override defaults, but exclude GitHub buttons and password toggle */\n        .authiqa-container button:not(.authiqa-github-button):not(.password-toggle),\n        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle),\n        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle) {\n            background-color: ').concat(B.buttonBackground," !important;\n            color: ").concat(B.buttonText," !important;\n            height: ").concat(D.height||"40px"," !important;\n            width: ").concat(D.width||"100%"," !important;\n            border-radius: ").concat(D.borderRadius,' !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle):hover,\n        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle):hover {\n            background-color: ').concat(D.hoverBackground||B.buttonBackground," !important;\n        }\n\n        /* GitHub button specific styling with proper interactivity */\n        .authiqa-container .authiqa-github-button {\n            background-color: #ffffff !important;\n            color: #000000 !important;\n            border: 1px solid #30363d !important;\n            border-radius: 4px !important;\n            padding: 0.5rem 1rem !important;\n            font-size: 1rem !important;\n            font-weight: bold !important;\n            width: 100% !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            gap: 0.5rem !important;\n            cursor: pointer !important;\n            transition: all 0.2s ease !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container .authiqa-github-button:hover {\n            background-color: #f6f8fa !important;\n            border-color: #1f2328 !important;\n            transform: translateY(-1px) !important;\n            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\n        }\n        .authiqa-container .authiqa-github-button:active {\n            transform: scale(0.98) !important;\n            box-shadow: none !important;\n            background-color: #eaeef2 !important;\n        }\n        \n        /* Label styling */\n        .authiqa-container .authiqa-label,\n        .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.5rem !important;\n            padding-left: 0.09rem !important;\n            font-weight: ").concat(N.labelFontWeight||"400"," !important;\n            color: ").concat(N.labelColor||(G?"#ffffff":B.inputText||"#333333")," !important;\n            font-size: ").concat(N.labelSize||"0.9rem",' !important;\n            height: auto !important;\n            line-height: 1.2 !important;\n        }\n        \n        /* Dark theme specific styles */\n        .authiqa-container[data-theme="dark"] .authiqa-label,\n        .authiqa-container[data-theme="dark"] label {\n            color: #ffffff !important;\n        }\n\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1.2rem (about 5px less) */\n        }\n\n        /* Ensure password container properly styles the label */\n        .authiqa-container .authiqa-password-container .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.3rem !important; /* Changed to 0.3rem (approximately 5px) */\n            padding-left: 0.08rem !important; /* Added left padding to move labels slightly right */\n            font-weight: 500 !important;\n            height: 14px !important; /* Added fixed height */\n            line-height: 14px !important; /* Added line height to match height */\n        }\n\n        /* Password field container */\n        .authiqa-container .authiqa-password-container {\n            position: relative !important;\n            width: 100% !important;\n        }\n\n        /* Password toggle button */\n        .authiqa-container .password-toggle {\n            position: absolute !important;\n            right: 12px !important;\n            top: 50% !important;\n            transform: translateY(-50%) !important;\n            background: none !important;\n            border: none !important;\n            color: ').concat(B.inputText?B.inputText+"99":"#a1a1aa",' !important;\n            cursor: pointer !important;\n            padding: 0 !important;\n            margin: 0 !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            height: 100% !important;\n            width: 40px !important;\n            z-index: 2 !important;\n        }\n        \n        /* Terms container - adjusted spacing */\n        .authiqa-container .terms-container {\n            display: flex !important;\n            align-items: flex-start !important;\n            margin: 0.5rem 0 1rem 0 !important; /* Decreased bottom margin from 3rem to 1rem */\n            position: relative !important;\n        }\n\n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin: 0.25rem 0.5rem 0 0 !important; /* Standardized margins */\n            position: static !important; /* Remove relative positioning */\n        }\n\n        .authiqa-container .terms-container label {\n            color: ').concat((null===(u=N.termsText)||void 0===u?void 0:u.textColor)||B.inputText||"#333333"," !important;\n            font-size: 0.875rem !important;\n            line-height: 1.4 !important;\n            margin: 0 !important;\n            padding-top: 0 !important;\n            margin-left: 0 !important;\n            flex: 1 !important;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat((null===(d=N.termsText)||void 0===d?void 0:d.linkColor)||B.buttonBackground||"#000000",' !important;\n            text-decoration: none !important;\n        }\n        \n        /* Input field styling - highest priority for user customization */\n        .authiqa-container input[type="text"],\n        .authiqa-container input[type="email"],\n        .authiqa-container input[type="password"],\n        .authiqa-input {\n            width: ').concat((null===(l=this.customization.inputs)||void 0===l?void 0:l.width)||"100%"," !important;\n            height: ").concat((null===(h=this.customization.inputs)||void 0===h?void 0:h.height)||"50px"," !important;\n            padding: ").concat((null===(p=this.customization.inputs)||void 0===p?void 0:p.padding)||"0 1rem"," !important;\n            font-size: ").concat((null===(m=this.customization.inputs)||void 0===m?void 0:m.fontSize)||"1rem"," !important;\n            font-weight: ").concat((null===(g=this.customization.inputs)||void 0===g?void 0:g.fontWeight)||"400"," !important;\n            border-radius: ").concat((null===(f=this.customization.inputs)||void 0===f?void 0:f.borderRadius)||"4px"," !important;\n            background-color: ").concat(B.inputBackground," !important;\n            color: ").concat(B.inputText," !important;\n            border: 1px solid ").concat(B.borderColor," !important;\n            margin: ").concat((null===(v=this.customization.inputs)||void 0===v?void 0:v.margin)||"0 0 1rem 0",' !important;\n            box-sizing: border-box !important;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;\n        }\n        .authiqa-container input[type="text"]:focus,\n        .authiqa-container input[type="email"]:focus,\n        .authiqa-container input[type="password"]:focus,\n        .authiqa-input:focus {\n            border-color: ').concat((null===(b=this.customization.inputs)||void 0===b?void 0:b.focusBorderColor)||"#000000"," !important;\n            box-shadow: ").concat((null===(y=this.customization.inputs)||void 0===y?void 0:y.focusBoxShadow)||"none",' !important;\n            outline: none !important;\n        }\n\n        /* Checkbox specific styling */\n        .authiqa-container input[type="checkbox"] {\n            width: auto !important;\n            height: auto !important;\n            margin-right: 8px !important;\n            margin-top: 3px !important;\n            background-color: transparent !important;\n        }\n\n        /* Decrease spacing between password field and terms */\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 0.5rem !important; /* Decreased from 1rem to 0.5rem */\n        }\n\n        /* Button spacing - no need to change as the terms container\'s bottom margin will create space */\n        .authiqa-container form button[type="submit"] {\n            margin-top: 0 !important; /* Remove top margin as we\'re using bottom margin on terms container */\n        }\n\n        .authiqa-container input[type="text"]::placeholder,\n        .authiqa-container input[type="email"]::placeholder,\n        .authiqa-container input[type="password"]::placeholder,\n        .authiqa-input::placeholder {\n            color: ').concat(B.inputPlaceholder||"#a3a3a3"," !important;\n            text-align: ").concat((null===(w=this.customization.inputs)||void 0===w?void 0:w.placeholderAlign)||"left"," !important;\n        }\n\n        /* Navigation (alternate-action) styling */\n        .authiqa-container .alternate-action {\n            text-align: ").concat((null===(x=this.customization.navLinks)||void 0===x?void 0:x.textAlign)||"center"," !important;\n            margin-top: ").concat((null===(S=this.customization.navLinks)||void 0===S?void 0:S.marginTop)||"1.5rem"," !important;\n            margin-bottom: ").concat((null===(A=this.customization.navLinks)||void 0===A?void 0:A.marginBottom)||"0"," !important;\n            font-size: ").concat((null===(q=this.customization.navLinks)||void 0===q?void 0:q.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(k=this.customization.navLinks)||void 0===k?void 0:k.color)||"var(--authiqa-nav-text-color, #1a1a1a)"," !important;\n            font-family: ").concat((null===(C=this.customization.navLinks)||void 0===C?void 0:C.fontFamily)||N.fontFamily," !important;\n            font-weight: ").concat((null===(T=this.customization.navLinks)||void 0===T?void 0:T.fontWeight)||"400",' !important;\n        }\n        .authiqa-container[data-theme="dark"] .alternate-action {\n            color: var(--authiqa-nav-text-color-dark, #ffffff) !important;\n        }\n        .authiqa-container .alternate-action a {\n            color: ').concat((null===(P=this.customization.navLinks)||void 0===P?void 0:P.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(E=this.customization.navLinks)||void 0===E?void 0:E.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .alternate-action a:hover {\n            text-decoration: underline !important;\n        }\n        /* Forgot password link styling */\n        .authiqa-container .forgot-password {\n            text-align: ").concat((null===(L=this.customization.navLinks)||void 0===L?void 0:L.textAlign)||"right"," !important;\n            margin-top: -1rem !important;\n            margin-bottom: 1rem !important;\n            font-size: ").concat((null===(I=this.customization.navLinks)||void 0===I?void 0:I.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(_=this.customization.navLinks)||void 0===_?void 0:_.color)||"#525252"," !important;\n            font-family: ").concat((null===(z=this.customization.navLinks)||void 0===z?void 0:z.fontFamily)||N.fontFamily," !important;\n            font-weight: ").concat((null===(M=this.customization.navLinks)||void 0===M?void 0:M.fontWeight)||"400"," !important;\n        }\n        .authiqa-container .forgot-password a {\n            color: ").concat((null===(O=this.customization.navLinks)||void 0===O?void 0:O.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(R=this.customization.navLinks)||void 0===R?void 0:R.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .forgot-password a:hover {\n            text-decoration: underline !important;\n        }\n    ")},t}();e.StyleGenerator=a},92:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.generateTermsContainerStyles=e.getComponentStyles=e.getStyleContent=void 0;var i=n(149);e.getStyleContent=function(t){return"\n        /* Dynamically generated styles for ".concat(t," theme */\n        :root {\n            --authiqa-bg-color: ").concat("dark"===t?"#18181b":"#ffffff",";\n            --authiqa-text-color: ").concat("dark"===t?"#ffffff":"#1a1a1a",";\n            --authiqa-border-color: ").concat("dark"===t?"#3f3f46":"#e5e5e5",";\n            --authiqa-input-bg: ").concat("dark"===t?"#27272a":"#ffffff",";\n            --authiqa-button-bg: ").concat("dark"===t?"#ffffff":"#18181b",";\n            --authiqa-button-text: ").concat("dark"===t?"#18181b":"#ffffff",";\n        }\n    ")},e.getComponentStyles=function(t){void 0===t&&(t="light");var e=i.THEMES[t];return{modal:{overlay:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:e.modalOverlay,zIndex:1e3},container:{position:"relative",width:"500px",margin:"50px auto",backgroundColor:e.background,color:e.text,borderRadius:"8px",padding:"20px",border:"1px solid ".concat(e.border)}},iframe:{border:"none",width:"100%",height:"600px",backgroundColor:e.background},message:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",padding:"12px 24px",borderRadius:"4px",fontSize:"14px",fontWeight:"500",opacity:"0",transition:"opacity 0.3s ease"},messageSuccess:{backgroundColor:"#4CAF50",color:"white"},messageError:{backgroundColor:"#f44336",color:"white"},messageShow:{opacity:"1"}}},e.generateTermsContainerStyles=function(t){var e;if(!(null===(e=t.customization)||void 0===e?void 0:e.colors))return"";var n=t.customization.colors;return'\n        /* Terms container styling */\n        .authiqa-container .terms-container {\n            display: flex;\n            align-items: flex-start;\n            margin: 0.75rem 0;\n        }\n        \n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin-top: 3px;\n            margin-right: 8px;\n        }\n        \n        .authiqa-container .terms-container label {\n            color: '.concat(n.inputText||"#333333",";\n            font-size: 0.875rem;\n            line-height: 1.4;\n            margin: 0;\n            flex: 1;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat(n.buttonBackground||"#000000",";\n            text-decoration: none;\n        }\n        \n        .authiqa-container .terms-container a:hover {\n            text-decoration: underline;\n        }\n    ")}},733:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.validateCustomAuthPaths=e.validateAuthUrls=void 0,e.validateAuthUrls=function(t,e){if(!t)return{isValid:!1,message:"Authentication URLs are required"};for(var n=0,i=["signup","signin","verify","reset","update","resend","successful"];n<i.length;n++){var o=i[n];if(!t[o])return{isValid:!1,message:"".concat(o," is required")};try{if("https:"!==new URL(t[o]).protocol)return{isValid:!1,message:"".concat(o," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"Invalid URL format for ".concat(o)}}}return{isValid:!0,message:""}},e.validateCustomAuthPaths=function(t,e){for(var n=0,i=Object.entries(t);n<i.length;n++){var o=i[n],a=o[0],r=o[1];if(r)try{if("https:"!==new URL(r).protocol)return{isValid:!1,message:"".concat(a," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"".concat(a," must be a complete URL (e.g., https://domain.com/path)")}}}return{isValid:!0,message:""}}}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var a=e[i]={id:i,exports:{}};return t[i].call(a.exports,a,a.exports,n),a.exports}return n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nc=void 0,n(156)})()));