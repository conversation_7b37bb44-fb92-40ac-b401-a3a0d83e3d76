{"name": "authiqa-react", "version": "1.0.2", "description": "A simple React component by <PERSON><PERSON><PERSON>", "main": "./dist/authiqa-react.umd.js", "module": "./dist/authiqa-react.mjs", "exports": {".": {"import": "./dist/authiqa-react.mjs", "require": "./dist/authiqa-react.umd.js"}}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "test": "vitest run"}, "repository": {"type": "git", "url": "git+https://github.com/Natuvea/authiqa-react.git"}, "keywords": ["react", "component", "authiqa"], "author": "Authiqa Team", "license": "MIT", "bugs": {"url": "https://github.com/Natuvea/authiqa-react/issues"}, "homepage": "https://github.com/Natuvea/authiqa-react#readme", "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "dependencies": {}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@vitejs/plugin-react": "^4.4.1", "c8": "^10.1.3", "eslint": "^9.25.1", "eslint-plugin-react": "^7.37.5", "jsdom": "^26.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-test-renderer": "^19.1.0", "vite": "^6.3.4", "vitest": "^3.1.2"}}