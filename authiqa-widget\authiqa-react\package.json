{"name": "authiqa-react", "version": "1.1.0", "description": "React components for Authiqa authentication", "main": "./dist/authiqa-react.umd.js", "module": "./dist/authiqa-react.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/authiqa-react.mjs", "require": "./dist/authiqa-react.umd.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build && tsc --emitDeclarationOnly", "test": "vitest run"}, "repository": {"type": "git", "url": "git+https://github.com/Natuvea/authiqa-react.git"}, "keywords": ["react", "component", "authiqa", "authentication", "auth"], "author": "Authiqa Team", "license": "MIT", "bugs": {"url": "https://github.com/Natuvea/authiqa-react/issues"}, "homepage": "https://github.com/Natuvea/authiqa-react#readme", "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-dts": "^3.5.2", "vitest": "^0.34.3"}}