{"name": "vue-template-compiler", "version": "2.7.16", "description": "template compiler for Vue 2.0", "main": "index.js", "unpkg": "browser.js", "jsdelivr": "browser.js", "browser": "browser.js", "types": "types/index.d.ts", "files": ["types/*.d.ts", "*.js"], "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue.git"}, "keywords": ["vue", "compiler"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue/issues"}, "homepage": "https://github.com/vuejs/vue/tree/dev/packages/vue-template-compiler#readme", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}, "devDependencies": {"vue": "file:../.."}}