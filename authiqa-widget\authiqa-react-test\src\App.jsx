import React from 'react';
import { AuthiqaWidget } from 'authiqa-react';
import './App.css';

function App() {
  const handleSuccess = (data) => {
    console.log('Authentication successful:', data);
  };

  const handleError = (error) => {
    console.error('Authentication error:', error);
  };

  return (
    <div className="App">
      <h1>Testing Authiqa React Component</h1>
      <p style={{ color: '#0a0', fontWeight: 600 }}>
        Google SSO will appear if enabled for your organization (no extra prop needed).
      </p>
      <div className="widget-container">
        <AuthiqaWidget 
          publicKey="APK_bfc0924ef0c7ef4ddbf83e4f650bb51d_1750438254"
          theme="light"
          action="signin"
          disableStyles={false}
          resetAuthPath="/reset-password"
          messages={{
            signinSuccess: 'Welcome!',
            signupSuccess: 'Account created!',
            resetSuccess: 'Reset link sent!'
          }}
          customization={{
            colors: {
              background: '#f9fafb',
              buttonBackground: '#10D5C6',
              buttonText: '#fff',
              inputBackground: '#fff',
              inputText: '#222',
              borderColor: '#10D5C6'
            },
            layout: {
              padding: '2rem',
              borderRadius: '12px',
              maxWidth: '420px'
            },
            typography: {
              titleColor: '#10D5C6',
              fontFamily: 'Segoe UI, sans-serif',
              titleText: { signinText: 'Sign In to Test' }
            }
          }}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      </div>
    </div>
  );
}

export default App;
